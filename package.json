{"name": "Smott_Hybrid", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@inplayer-org/inplayer.js": "^3.13.28", "@jwplayer/jwplayer-react-native": "^1.0.3", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/material-top-tabs": "^7.2.10", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@reduxjs/toolkit": "^2.5.1", "@types/react-native-snap-carousel": "^3.8.11", "axios": "^1.7.9", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.3.1", "react-native": "0.77.0", "react-native-bootsplash": "^6.3.3", "react-native-config": "^1.5.5", "react-native-element-dropdown": "^2.12.4", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.22.1", "react-native-linear-gradient": "^2.8.3", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-orientation": "^3.1.3", "react-native-pager-view": "^6.7.1", "react-native-paper": "^5.13.1", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "^3.16.7", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.5.0", "react-native-side-menu": "^1.1.3", "react-native-snap-carousel": "^3.9.1", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^4.0.10", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.11.0", "react-native-webview": "13.13.2", "react-redux": "^9.2.0", "toggle-switch-react-native": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@eslint/js": "^9.18.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.0", "@react-native/eslint-config": "0.77.0", "@react-native/metro-config": "0.77.0", "@react-native/typescript-config": "0.77.0", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4", "typescript-eslint": "^8.21.0"}, "engines": {"node": ">=18"}}