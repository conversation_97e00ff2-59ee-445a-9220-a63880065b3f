diff --git a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
index dae71a3..72b500c 100644
--- a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
+++ b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
@@ -43,8 +43,8 @@ export default class Carousel extends Component {
         autoplayDelay: PropTypes.number,
         autoplayInterval: PropTypes.number,
         callbackOffsetMargin: PropTypes.number,
-        containerCustomStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
-        contentContainerCustomStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerCustomStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
+        contentContainerCustomStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         enableMomentum: PropTypes.bool,
         enableSnap: PropTypes.bool,
         firstItem: PropTypes.number,
@@ -61,7 +61,7 @@ export default class Carousel extends Component {
         scrollEnabled: PropTypes.bool,
         scrollInterpolator: PropTypes.func,
         slideInterpolatedStyle: PropTypes.func,
-        slideStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        slideStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         shouldOptimizeUpdates: PropTypes.bool,
         swipeThreshold: PropTypes.number,
         useScrollView: PropTypes.oneOfType([PropTypes.bool, PropTypes.func]),
diff --git a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
index 5c021cf..2ea383d 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
@@ -3,7 +3,6 @@ import { I18nManager, Platform, View, ViewPropTypes } from 'react-native';
 import PropTypes from 'prop-types';
 import PaginationDot from './PaginationDot';
 import styles from './Pagination.style';
-
 const IS_IOS = Platform.OS === 'ios';
 const IS_RTL = I18nManager.isRTL;
 
@@ -14,16 +13,16 @@ export default class Pagination extends PureComponent {
         dotsLength: PropTypes.number.isRequired,
         activeOpacity: PropTypes.number,
         carouselRef: PropTypes.object,
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         dotColor: PropTypes.string,
-        dotContainerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        dotContainerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         dotElement: PropTypes.element,
-        dotStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        dotStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         inactiveDotColor: PropTypes.string,
         inactiveDotElement: PropTypes.element,
         inactiveDotOpacity: PropTypes.number,
         inactiveDotScale: PropTypes.number,
-        inactiveDotStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        inactiveDotStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         renderDots: PropTypes.func,
         tappableDots: PropTypes.bool,
         vertical: PropTypes.bool,
diff --git a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
index e59d196..99ca1d5 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
@@ -12,11 +12,11 @@ export default class PaginationDot extends PureComponent {
         activeOpacity: PropTypes.number,
         carouselRef: PropTypes.object,
         color: PropTypes.string,
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         inactiveColor: PropTypes.string,
-        inactiveStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        inactiveStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         index: PropTypes.number,
-        style: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         tappable: PropTypes.bool
     };
 
diff --git a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
index 8bc774a..3e36f57 100644
--- a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
+++ b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
@@ -1,9 +1,10 @@
 // Parallax effect inspired by https://github.com/oblador/react-native-parallax/
 
 import React, { Component } from 'react';
-import { View, ViewPropTypes, Image, Animated, Easing, ActivityIndicator, findNodeHandle } from 'react-native';
+import { View, Image, Animated, Easing, ActivityIndicator, findNodeHandle, Platform } from 'react-native';
 import PropTypes from 'prop-types';
 import styles from './ParallaxImage.style';
+import { ViewStyle } from 'react-native'
 
 export default class ParallaxImage extends Component {
 
@@ -16,7 +17,7 @@ export default class ParallaxImage extends Component {
         sliderHeight: PropTypes.number, // passed from <Carousel />
         sliderWidth: PropTypes.number, // passed from <Carousel />
         vertical: PropTypes.bool, // passed from <Carousel />
-        containerStyle: ViewPropTypes ? ViewPropTypes.style : View.propTypes.style,
+        containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
         dimensions: PropTypes.shape({
             width: PropTypes.number,
             height: PropTypes.number
