import React, { useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator, StackNavigationProp, TransitionPresets } from "@react-navigation/stack";
import {
  ActivityIndicator,
  Dimensions,
  Image,
  SafeAreaView,
  TouchableOpacity,
  View,
  Settings,
  Platform,
} from "react-native";
import SideMenu from "react-native-side-menu";
import Menu from "../components/Menu";
import TabNavigator from "./TabNavigator";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SettingsScreen from "../screens/SettingsScreen";
import DetailsScreen from "../components/DetailsScreen";
import ArticleDetailsScreen from "../components/ArticleDetailsScreen";
import SeasonScreen from "../screens/SeasonScreen";
import SeeMoreScreen from "../screens/SeeMoreScreen";
import MoreDetailsScreen from "../screens/MoreDeatailsScreen";
import Redirection from "../components/Redirection";
import SearchScreen from "../screens/SearchScreen";
import Favorites from "../screens/Favorites";
import WatchHistory from "../screens/WatchHistory";
import AppSettings from "../screens/AppSettings";
import PageNotAvailable from "../screens/PageNotAvailable";
import LoginPage from "../screens/LoginPage";
import SignUpPage from "../screens/SignupPage";
import FullscreenVideoPlayer from "../screens/FullScreenPlayer";
import ChampionshipStandingsScreen from "../screens/ChampionshipStandingsScreen";
import AppIdAuthScreen from "../screens/AppIdAuthScreen";
import YouTubeDetailsScreen from "../components/YouTubeDetailsScreen";
import LiveStreamScreen from "../screens/LiveStreaming";

type RootStackParamList = {
  Home: undefined;
  ChampionshipStandings: { selectedTab?: string; color?: any };
  [key: string]: any; // Allow any screen with any params for flexibility
};

const RootStack: React.FC = () => {
  const Stack = createStackNavigator<RootStackParamList>();
  const { colors, imageUrl, imageKey, isRTL } = useSelector((state: RootState) => state.homeScreen);
  const appSettings = useSelector(
    (state: RootState) => state.homeScreen.appSettings
  );
  const appId = useSelector((state: RootState) => state.authScreen.appId);
  const { width, height } = Dimensions.get("window");
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);

  // Handle menu item selection
  const onMenuItemSelected = () => {
    setIsOpen(false);
  };

  useEffect(() => {
    // Load stored App ID when the app starts
    setIsLoggedIn(!!appId);
  }, []);
  console.log(isLoggedIn);

  const menu = <Menu onItemSelected={onMenuItemSelected} />;

  if (isLoggedIn === null) {
    // Show a loading indicator while checking login status
    return (
      <View
        style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: 'white' }}
      >
        <ActivityIndicator size="large" color="#000000" />
      </View>
    );
  }
  const HomeHeaderSearchIcon = (navigation: StackNavigationProp<RootStackParamList>) => {
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.push("Search", {
            color: colors,
            appsettinglist: appSettings,
          });
        }}
        style={{
          height: width / 15,
          width: width / 10,
          marginLeft: isRTL ? width / 30 : 0,
        }}
      >
        <MaterialCommunityIcons
          name="magnify"
          color={colors.primaryHighlightColor}
          size={width / 16}
        />
      </TouchableOpacity>
    );
  }

  const HomeHeaderMenuIcon = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          setIsOpen(!isOpen);
        }}
        style={{
          height: width / 15,
          width: width / 10,
          marginLeft: isRTL ? 0 : width / 30,
        }}
      >
        <MaterialCommunityIcons
          name="menu"
          color={colors.primaryHighlightColor}
          size={width / 16}
        />
      </TouchableOpacity>
    );
  }

  const HeaderBackImage = (navigation: StackNavigationProp<RootStackParamList>) => {
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.goBack();
        }}
        style={{ height: width / 15, width: width / 10 }}
      >
        <MaterialCommunityIcons
          name="arrow-left"
          size={24}
          color="black" // Adjust color as needed
          style={{
            transform: [{ scaleX: isRTL ? -1 : 1 }], // Flip the icon in RTL mode
            marginLeft: isRTL ? "auto" : 10, // Adjust margin dynamically
            marginRight: isRTL ? 10 : 0,
          }}
        />
      </TouchableOpacity>
    );
  }

  const HeaderSearchIcon = (navigation: StackNavigationProp<RootStackParamList>, appSettings: Settings[]) => {
    return (
      <TouchableOpacity
        onPress={() => {
          navigation.push("Search", {
            color: colors,
            appsettinglist: appSettings,
          });
        }}
        style={{ height: width / 15, width: width / 10 }}
      >
        <MaterialCommunityIcons
          name="magnify"
          color={colors.primaryHighlightColor}
          size={width / 16}
          style={{
            transform: [{ scaleX: isRTL ? -1 : 1 }], // Flip the icon in RTL mode
            marginLeft: isRTL ? "auto" : 10, // Adjust margin dynamically
            marginRight: isRTL ? 10 : 0,
          }}
        />
      </TouchableOpacity>
    );
  }



  return (
    <SafeAreaView style={{ flex: 1 }} >
      <NavigationContainer>
        <SideMenu
          menu={menu}
          isOpen={isOpen}
          onChange={(open) => setIsOpen(open)}
          menuPosition={isRTL ? "right" : 'left'}
        >
          <Stack.Navigator
            initialRouteName={isLoggedIn ? "Home" : "AppIdAuthScreen"}
            screenOptions={{
              gestureEnabled: true, // Enables swipe gestures
              ...TransitionPresets.SlideFromRightIOS, // Default slide animation
              gestureDirection: isRTL ? "horizontal-inverted" : "horizontal", // Change direction based on RTL
              headerStyle: {
                backgroundColor: colors.secondaryBackgroundColor,
              },
              headerTintColor: colors.primaryHighlightColor,
              headerTitleStyle: { fontWeight: "bold" },
              headerBackTitleStyle: { fontSize: width / 25 },
              cardStyle: {
                backgroundColor: colors.secondaryBackgroundColor,
              },
            }}
          >
            <Stack.Screen
              name="Home"
              component={TabNavigator}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <View style={{ alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                    <Image
                      style={{ width: width / 5, height: 45 }}
                      source={{
                        uri: imageUrl + imageKey,
                      }}
                      resizeMode='contain'
                    />
                  </View>
                ),
                headerStyle: { height: Platform.OS === 'ios' ? height / 8 : 60 },
                headerBackTitle: "Back",
                headerRight: () => (
                  isRTL ? HomeHeaderMenuIcon() : HomeHeaderSearchIcon(navigation)
                ),
                headerLeft: () => (
                  isRTL ? HomeHeaderSearchIcon(navigation) : HomeHeaderMenuIcon()
                ),

              })}
            />
            <Stack.Screen
              name="Settings"
              component={SettingsScreen}
              options={{
                headerTitle: "",
                headerBackTitle: "Back",
              }}
            />
            <Stack.Screen
              name="Details"
              component={DetailsScreen}
              options={({ navigation }) => ({
                headerTitle: "",
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />
            <Stack.Screen
              name="ArticleDetails"
              component={ArticleDetailsScreen}
              options={({ navigation }) => ({
                headerTitle: "",
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />
            <Stack.Screen
              name="Season"
              component={SeasonScreen}
              options={({ navigation }) => ({
                headerTitle: "",
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />
            <Stack.Screen
              name="SeeMore"
              component={SeeMoreScreen}

              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                    resizeMode='contain'
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />
            <Stack.Screen
              name="MoreDetail"
              component={MoreDetailsScreen}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                    resizeMode='stretch'
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? HeaderSearchIcon(navigation, appSettings) : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : HeaderSearchIcon(navigation, appSettings)
              })}
            />
            <Stack.Screen
              name="Redirection"
              component={Redirection}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="Search"
              component={SearchScreen}
              options={({ navigation }) => ({
                headerShown: false,
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="favourites"
              component={Favorites}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',

                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="watchhistory"
              component={WatchHistory}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="Appsettings"
              component={AppSettings}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="pagenotfound"
              component={PageNotAvailable}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="Login"
              component={LoginPage}
              options={({ navigation }) => ({
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerShown: false,
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="SignUp"
              component={SignUpPage}
              options={({ navigation }) => ({
                headerShown: false,
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{ width: width / 5, height: width / 17 }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="ChampionshipStandings"
              component={ChampionshipStandingsScreen}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen
              name="FullScreen"
              component={FullscreenVideoPlayer}
              options={({ navigation }) => ({
                headerShown: false,
                headerTitleAlign: isRTL ? 'center' : 'left',
                headerTitle: () => (
                  <Image
                    style={{
                      width: width / 5, height: width / 17

                    }}
                    source={{
                      uri: imageUrl + imageKey,
                    }}
                  />
                ),
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
              })}
            />
            <Stack.Screen
              name="AppIdAuthScreen"
              component={AppIdAuthScreen}
              options={{
                headerShown: false
              }}
            />
            <Stack.Screen
              name="YouTubeDetailsScreen"
              component={YouTubeDetailsScreen}
              options={({ navigation }) => ({
                headerTitle: "",
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />
            <Stack.Screen
              name="LiveStreamScreen"
              component={LiveStreamScreen}
              options={({ navigation }) => ({
                headerTitle: "",
                headerBackTitle: "Back",
                headerLeft: () => isRTL ? null : HeaderBackImage(navigation),
                headerRight: () => isRTL ? HeaderBackImage(navigation) : null,
              })}
            />

          </Stack.Navigator>
        </SideMenu>
      </NavigationContainer>
    </SafeAreaView >
  );
};

export default RootStack;
