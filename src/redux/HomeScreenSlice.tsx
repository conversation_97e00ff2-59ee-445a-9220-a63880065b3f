import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface HomeScreenState {
  colors: Color;
  brand: Branding[];
  menu: Menu[];
  topMenu: Menu[],
  appSettings: Settings[];
  imageKey: string;
  imageUrl: string;
  isRTL: boolean; // applanguage alignment
  appLanguage: string;
  videoCloud: VideoCloud[];
}

// Default colors to prevent undefined errors during app initialization
const defaultColors: Color = {
  _id: '',
  primaryBackgroundColor: '#ffffff',
  secondaryBackgroundColor: '#f5f5f5',
  primaryTextColor: '#000000',
  secondaryTextColor: '#666666',
  primaryHighlightColor: '#007AFF',
  secondaryHighlightColor: '#5856D6',
  application: '',
  date: '',
  __v: 0,
  menuBackgroundColor: '#ffffff',
  elementBackgroundColor: '#f0f0f0',
  elementForegroundColor: '#000000',
  textShadow: false,
  dark: false,
  buttonBackgroundColor: '#007AFF',
  buttonForegroundColor: '#ffffff',
  menuForegroundColor: '#000000',
};

// Initial state
const initialState: HomeScreenState = {
  colors: defaultColors,
  brand: [],
  menu: [],
  topMenu: [],
  appSettings: [],
  imageKey: '',
  imageUrl: 'https://smottstorage.s3.us-east-2.amazonaws.com/',
  isRTL: false,
  appLanguage: 'en',
  videoCloud: [],
};

// Creating the slice
const homeScreenSlice = createSlice({
  name: 'homeScreen',
  initialState,
  reducers: {
    updateColors: (state, action: PayloadAction<Color>) => {
      state.colors = action.payload;
    },
    updateBrand: (state, action: PayloadAction<Branding[]>) => {
      state.brand = action.payload;
    },
    updateMenu: (state, action: PayloadAction<Menu[]>) => {
      state.menu = action.payload;
    },
    updateTopMenu: (state, action: PayloadAction<Menu[]>) => {
      state.topMenu = action.payload;
    },
    updateAppSettings: (state, action: PayloadAction<Settings[]>) => {
      state.appSettings = action.payload;
    },
    updateImageKey: (state, action: PayloadAction<string>) => {
      state.imageKey = action.payload;
    },
    updateAppLanguage: (state, action: PayloadAction<string>) => {
      state.appLanguage = action.payload;
    },
    updateAppLangDirection: (state, action: PayloadAction<boolean>) => {
      state.isRTL = action.payload;
    },
    updateVideoInfo: (state, action: PayloadAction<VideoCloud[]>) => {
      state.videoCloud = action.payload;
    },
  },
});

// Exporting actions and reducer
export const { updateColors, updateBrand, updateMenu, updateTopMenu, updateAppSettings, updateImageKey, updateAppLanguage, updateAppLangDirection, updateVideoInfo } =
  homeScreenSlice.actions;
export default homeScreenSlice.reducer;

// Color Interface
export interface Color {
  _id: string;
  primaryBackgroundColor: string;
  secondaryBackgroundColor: string;
  primaryTextColor: string;
  secondaryTextColor: string;
  primaryHighlightColor: string;
  secondaryHighlightColor: string;
  application: string;
  date: string;
  __v: number;
  menuBackgroundColor: string;
  elementBackgroundColor: string;
  elementForegroundColor: string;
  textShadow: boolean;
  dark: boolean;
  buttonBackgroundColor: string;
  buttonForegroundColor: string;
  menuForegroundColor: string;
}

interface Branding {
  _id: string;
  pageTitle: string | null;
  mainLogo: string;
  footerLogo: string;
  iconLogo: string;
  footerDescription: string | null;
  application: string;
  date: string;
  __v: number;
  appIcon: string;
  chromeCastLogo: string;
  marketingLogo: string;
  mobileLogo: string;
  tvLogo: string;
  chromeCastDarkLogo: string;
  footerDarkLogo: string;
  mainDarkLogo: string;
  mobileDarkLogo: string;
  tvDarkLogo: string;
}

interface MenuItem {
  _id: string;
  menuPage: string;
  menuPageRef: string;
  menuPageType: string;
  menuUrlName: string;
  menuTitle: string;
  menuOrder: string;
  menuIcon: string;
}

interface Menu {
  _id: string;
  menuTitle: string;
  urlName: string;
  menuType: string;
  menuPage: string;
  items: MenuItem[];
  externalUrl: string | null;
  internalUrl: string | null;
  menuIcon: string;
  menuOrder: string;
  application: string;
  date: string;
  __v: number;
  position: string;
}

// Define the Setting interface
export interface Settings {
  _id: string;
  title: string;
  headData: string;
  domainName: string;
  startPage: string;
  allowLogin: boolean;
  allowSubscription: boolean;
  application: string;
  date: string;
  __v: number;
  siteDescription: string;
  bingeWatching: boolean;
  chromecastID: string;
  favourites: boolean;
  gdpr: boolean;
  multiLanguage: boolean;
  premiumIcon: string;
  premiumIconVisibility: boolean;
  resumePlayback: boolean;
  rtl: boolean;
  socialSharing: boolean;
  watchHistory: boolean;
  production: boolean;
  darkMode: boolean;
  RFP: boolean;
}

export interface VideoCloud {
  _id: string,
  cloudName: string,
  liveCloudName: string | null,
  cloudKey: string,
  cloudSecret: string,
  cloudKeyV2: string,
  cloudSecretV2: string,
  mainAccountId: string,
  secondAccountId: string | null,
  firstPlayer: string,
  secondPlayer: string,
  premiumPlayer: string,
  licenseKey: string,
  iosLicenseKey: string,
  premLicenseKey: string,
  iosPremLicenseKey: string,
  secLicenseKey: string,
  iosSecLicenseKey: string,
  drmKey: string,
  drmMobileKey: string,
  drmTVKey: string | null,
  searchPlaylist: string | null,
  recommendedPlaylist: string | null,
  watchPlaylist: string | null,
  application: string,
  date: string,
  __v: number

}
