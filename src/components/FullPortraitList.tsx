import React, { useEffect, useState } from 'react';
import { FlatList, Text, Dimensions, View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import { Linking } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { BigPortraitListProps, JwPlaylistItem } from './BigPortraitList';
import { VideoPlayerService } from '../services/VideoPlayerService';

const { width } = Dimensions.get('window');

const FullPortraitList: React.FC<BigPortraitListProps> = (props) => {
  const navigation = useNavigation();
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<unknown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData: {
    imageUrl: string;
    data: unknown;
    pageType: string;
    itemnavigation: string;
    urlname: string;
    tag: string;
    mainHeader: string;
  }[] = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);

      }
    } catch (error) {
      console.error('Error fetching playlist [FullPortraitList]:', error);
    }
  }

  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.map((item, key) => {
        FullData.push({
          imageUrl: imageUrl + (item.itemid.portraitThumbnail ? item.itemid.portraitThumbnail.imagekey : ''),
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          tag: item.itemid.tag,
          mainHeader: item.itemid.mainHeader,
        });
      });
    }
    if (JwData.playlist != null) {
      JwData.playlist.forEach((item, key) => {
        FullData.push({
          imageUrl: item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }

  }

  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: unknown,
    urlname: string
  ) => {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType == 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', {
          item: itemdata,
          color: props.color,
        });
      } else {
        return navigation.push('Details', {
          item: itemdata,
          color: props.color,
        });
      }
    } else if (pageType == 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 25 }}>
        <View style={styles(color, isRTL).row}></View>
        <View style={styles(color, isRTL).inputWrap}>
          <Text style={styles(color, isRTL).text1}>{props.listtitle}</Text>
        </View>
        <View style={{ marginBottom: width / 45 }}>
          <FlatList
            style={{ marginTop: width / 50 }}
            numColumns={3} // set number of columns
            // columnWrapperStyle={styles(color,isRTL).row}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={FullData}
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => {
                  const hasItemId = item.data.itemid !== undefined;
                  const isIOSDevice = hasItemId && item.data.itemid.devices[2].itemName === 'iOS';
                  const isVisible = hasItemId && item.data.itemid.devices[2].itemVisibility === true;

                  if ((isIOSDevice && isVisible) || !hasItemId) {
                    handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname);
                  } else {
                    navigation.push('pagenotfound', {
                      color: props.color,
                    });
                  }
                }}>
                <Image
                  PlaceholderContent={<ActivityIndicator />}
                  source={{ uri: item.imageUrl != null ? item.imageUrl : null }}
                  style={{
                    width: width / 3.2,
                    height: width / 2.2,
                    borderWidth: 0,
                    borderColor: '#d35647',
                    borderRadius:
                      props.appsettings !== undefined
                        ? props.appsettings.imageBorder === false
                          ? width / 50
                          : 0
                        : null,
                    // resizeMode:'contain',
                    margin: width / 95,
                    // marginTop:10
                  }}
                />
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    )
  );
};

export default FullPortraitList;


const styles = (color, isRTL: boolean) => StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: isRTL ? 'flex-end' : 'space-around',
    direction: isRTL ? 'rtl' : 'ltr',

  },
  inputWrap: {
    flex: 1,
    borderColor: color.primaryTextColor,
    alignSelf: isRTL ? 'flex-end' : 'flex-start',
    direction: isRTL ? 'rtl' : 'ltr',
  },
  text1: {
    color: color.primaryTextColor,
    fontSize: width / 25,
    fontWeight: 'bold',
    marginHorizontal: width / 40,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: color.primaryTextColor,
    fontSize: width / 30,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    marginHorizontal: width / 40,
  },
});