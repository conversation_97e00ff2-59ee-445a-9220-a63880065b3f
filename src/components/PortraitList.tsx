import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { Image } from 'react-native';
import { VideoPlayerService } from '../services/VideoPlayerService';

const { width } = Dimensions.get('window');

const PortraitList = (props) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState<unkown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [PortraitList]:', error);
    }
  }

  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.map((item, key) => {
        FullData.push({
          imageUrl: imageUrl + item.itemid.portraitThumbnail.imagekey,
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
        });
      });
    }
    if (JwData.playlist != null) {
      JwData.playlist.forEach((item, key) => {
        FullData.push({
          imageUrl: item.PortraitImage != '' ? item.PortraitImage : item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
        });
      });
    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }

  }

  function navigationlist(pageType, itemnavigation, itemdata, urlname) {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType == 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType == 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  return (
    setPlaylistData(),
    (
      <View style={{ marginBottom: width / 25, marginTop: width / 60 }}>
        <View style={styles(colors, isRTL).row}>
          <View style={styles(colors, isRTL).inputWrap}>
            <Text style={styles(colors, isRTL).text1}>{props.listtitle}</Text>
          </View>
          <View style={styles(colors, isRTL).inputWrap}>
            {props.seemoretextvis == true && props.seemoretext != null ? (
              <TouchableOpacity
                style={styles(colors, isRTL).text2}
                onPress={() => {
                  navigation.navigate('SeeMore', {
                    itemId: props.listtitle,
                    listdatas: props.datas,
                    comp: props.seemorecomp,
                    color: props.color,
                  });
                }}>
                <Text style={styles(colors, isRTL).text2}>{props.seemoretext}</Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <FlatList
          style={{ marginTop: width / 50 }}
          horizontal
          showsHorizontalScrollIndicator={false}
          data={FullData}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => {
                const hasItemId = item.data.itemid !== undefined;
                const isIOSDevice = hasItemId && item.data.itemid.devices[2].itemName === 'iOS';
                const isVisible = hasItemId && item.data.itemid.devices[2].itemVisibility === true;

                if ((isIOSDevice && isVisible) || !hasItemId) {
                  navigationlist(item.pageType, item.itemnavigation, item.data, item.urlname);
                } else {
                  navigation.push('pagenotfound', {
                    color: props.color,
                  });
                }
              }}>
              {item.imageUrl != null ? (
                <Image
                  PlaceholderContent={<ActivityIndicator />}
                  source={{
                    uri: item.imageUrl != null ? item.imageUrl : null,
                  }}
                  style={{
                    width: width / 4,
                    height: width / 2.8,
                    borderWidth: 0,
                    borderColor: '#d35647',
                    borderRadius:
                      props.appsettings !== undefined
                        ? props.appsettings.imageBorder === false
                          ? width / 50
                          : 0
                        : null,
                    // resizeMode:'contain',
                    margin: width / 150,
                    marginBottom: width / 50,
                  }}
                />
              ) : null}
            </TouchableOpacity>
          )}
        />
      </View>
    )
  );
};

export default PortraitList;


const styles = (colors, isRTL) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'row',
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    left: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    textAlign: 'right',
    right: width / 40,
    textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
  },
});