import React from 'react';
import { useEffect, useState } from 'react';
import { Text, View, StyleSheet, Dimensions, TouchableOpacity, Linking } from 'react-native';
import CarouselItem from './CarouselItem';
import { useNavigation } from '@react-navigation/native';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { scrollInterpolator, animatedStyles } from '../utils/animationmovie';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import constant from '../config/constant';
import { VideoPlayerService } from '../services/VideoPlayerService';
import Feather from 'react-native-vector-icons/Feather';
 
 
 
const MovieCarouselList = (props) => {
  const navigation = useNavigation();
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [JwData, setJwData] = useState([]);
 
  const isCarousel = React.useRef(null);
  const [indexs, setIndex] = React.useState(0);
  const { width: SLIDER_WIDTH, height: SLIDER_HEIGHT } = Dimensions.get("window");
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const playlistInfo: ArrayLike<any> & readonly any[] = [];
 
  useEffect(() => {
    initVideoInfo();
  }, []);
 
  const initVideoInfo = async () => {
    try {
      if (props.datas.playlistId === null) {
        return;
      }
      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [MovieCarouselList]:', error);
    }
  }
 
  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: imageUrl + item.itemid.portraitThumbnail.imagekey,
          movieid: item.itemid.videoId != null ? item.itemid.videoId : '',
          trailerid: '',
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage != null ? item.itemid.externalPage : '',
          urlname: item.itemid.urlName,
          videoimage: constant.imageurl + item.itemid.landscapeThumbnail.imagekey,
        });
      });
    }
    if (JwData.playlist != null) {
      JwData.playlist.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.PortraitImage,
          movieid: item.MovieId,
          trailerid: item.TrailerId,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          videoimage: item.image,
        });
      });
    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        playlistInfo.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }
 
  const handleNavigationTo = (
    pageType: string,
    itemnavigation: string,
    itemdata: unknown,
    urlname: string
  ) => {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType == 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', {
          item: itemdata,
          color: props.color,
        });
      } else {
        return navigation.push('Details', {
          item: itemdata,
          color: props.color,
        });
      }
    } else if (pageType == 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }
 
  const CarouselCardItem = ({ item, index }) => {
    return (
      <View key={index} style={[styles.cardContainer, { height: props.height ? SLIDER_HEIGHT * props.height : SLIDER_HEIGHT * 0.5 }]}>  
        <View style={styles.headerContainer}>
          <Text style={styles.headerText}>{item.mainHeader}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            (item.data.itemid !== undefined
              ? item.data.itemid.devices[2].itemName == 'iOS'
              : null && item.data.itemid !== undefined
                ? item.data.itemid.devices[2].itemVisibility == true
                : null) || item.data.itemid == undefined
              ? handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname)
              : navigation.push('pagenotfound', {
                color: props.color,
              });
          }}
          style={styles.imageTouchable}
        >
          <CarouselItem item={item} color={props.color} />
        </TouchableOpacity>
        <View style={styles.bottomContainer}>
          <Text style={styles.bottomText}>{item.bottomText || 'BOTTOM TEXT'}</Text>
        </View>
      </View>
    );
  };
 
 
  return (
    setPlaylistData(),
 
    <View style={{}}>
      <View style={{ position: 'relative', justifyContent: 'center', alignItems: 'center' }}>
        <TouchableOpacity
          style={styles.leftButton}
          onPress={() => isCarousel.current?.snapToPrev()}
        >
          <Feather name="arrow-left" size={30} color="#fff" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.rightButton}
          onPress={() => isCarousel.current?.snapToNext()}
        >
          <Feather name="arrow-right" size={30} color="#fff" />
        </TouchableOpacity>
        <Carousel
          ref={isCarousel}
          data={playlistInfo}
          renderItem={CarouselCardItem}
          sliderWidth={SLIDER_WIDTH * 0.8}
          itemWidth={SLIDER_WIDTH * 0.8}
          containerCustomStyle={{
            marginVertical: 20,
            alignSelf: 'center',
            borderRadius: 10,
          }}
          inactiveSlideShift={0}
          onSnapToItem={(index) => setIndex(index)}
          scrollInterpolator={scrollInterpolator}
          slideInterpolatedStyle={animatedStyles}
          useScrollView={true}
          inactiveSlideScale={0.9}
          inactiveSlideOpacity={-1}
        />
      </View>
 
      {/* Pagination */}
      {/* <View style={{ marginTop: width / -8, bottom: -5 }}>
        <Pagination
          dotsLength={playlistInfo.length}
          activeDotIndex={indexs}
          carouselRef={isCarousel}
          // dotStyle={{
          //   width: 10,
          //   height: 5,
          //   borderRadius: 0,
          //   marginHorizontal: -8,
          //   backgroundColor: 'white',
          // }}
          // inactiveDotOpacity={0.4}
          // inactiveDotScale={0.6}
          // tappableDots={true}
          // containerStyle={{
          //   width: '100%',
          //   justifyContent: 'center',
          //   alignItems: 'center',
          // }}
        />
      </View> */}
    </View>
  )
};
 
const { width } = Dimensions.get('window');
 
const styles = StyleSheet.create({
  carouselContainer: {
    // marginTop: 20,
  },
  slide1: {
    // flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    paddingHorizontal: 10,
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold',
  },
  image: {
    width,
    flex: 1,
  },
  pagination_x: {
    position: 'absolute',
    bottom: 25,
    left: 0,
    right: 0,
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
 
  pagination_y: {
    position: 'absolute',
    right: 15,
    top: 0,
    bottom: 0,
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  textContainer: {
    position: 'absolute',
    bottom: 20,
    left: 10,
    right: 10,
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center'
  },
  leftButton: {
    position: 'absolute',
    left: 15,
    zIndex: 1,
    backgroundColor: 'grey',
    borderRadius: 25,
    padding: 8,
  },
  rightButton: {
    position: 'absolute',
    right: 15,
    zIndex: 1,
    backgroundColor: 'grey',
    borderRadius: 25,
    padding: 8,
  },
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'flex-start',
    shadowRadius: 6,
    marginHorizontal: 10,
    overflow: 'hidden',
  },
  headerContainer: {
    width: '100%',
    backgroundColor: 'white',
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    color: '#222',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageTouchable: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    backgroundColor: 'transparent',
  },
  bottomContainer: {
    width: '100%',
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomText: {
    color: '#444',
    fontSize: 14,
    textAlign: 'center',
  },
});
 
export default MovieCarouselList;