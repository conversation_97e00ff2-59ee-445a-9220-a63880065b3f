import * as React from 'react';
import {
  Text,
  Dimensions,
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Share,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/Ionicons';
import Swiper from 'react-native-swiper';
// import JWPlayer from './JWPlayer';
import LandscapeList from './LandscapeList';
import { useState } from 'react';
import axios from 'axios';
import { Image } from 'react-native-elements';
import { useFocusEffect } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay/lib';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { AppService } from '../services/AppService';

const { width } = Dimensions.get('window');

const ArticleDetailsScreen = ({ route }) => {
  const { item } = route.params;
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const color = useSelector((state: RootState) => state.homeScreen.colors);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [Recommend, setRecommend] = useState([]);
  const [spinner, setspinner] = useState(true);
  const styles = StyleSheet.create({
    watchnowbuttoncontainer: {
      flexDirection: 'row',
      backgroundColor: colors.primaryHighlightColor,
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / 22,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 1.1,
      height: width / 12,
      borderRadius: 5,
    },
    watchbuttoncontainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginLeft: width / 22,
      marginRight: width / -44,
      marginTop: 0,
      marginBottom: width / 40,
      width: width / 2 / 1.13,
      height: width / 12,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: colors.primaryTextColor,
      borderRadius: 5,
    },
    wrapper: {},
    slide1: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    text: {
      color: '#fff',
      fontSize: 30,
      fontWeight: 'bold',
    },
    image: {
      width,
      flex: 1,
      height: width / 1.77,
    },
  });

  const getdata = async () => {
    const token = await AppService.getToken();
    try {
      if (!item.itemid) {
        setspinner(false);
        return;
      }
      const result = await axios.get(
        AppService.baseUrl + '/api/view/' + AppService.appId + '/' +
        item.itemid._id +
        '/getPageFullItem',
        {
          headers: { Authorization: token },
        }
      );
      console.log('sdcvh', item.itemid.recommendation);
      setRecommend(result.data);
      setspinner(false);
    } catch (e) {
      setspinner(false);
      console.log(e);
      throw e;
    }
  };

  // useEffect(() => {
  //   Recommend.length == 0 ? getdata() : null;
  // }, [Recommend.length, getdata]);

  useFocusEffect(
    React.useCallback(() => {
      if (Recommend.length == 0) {
        getdata();
      }
    }, [Recommend.length, getdata])
  );

  const onShare = async () => {
    try {
      const result = await Share.share({
        message:
          'http://smottapp.com/share/index.php?type=season&title=' +
          item.itemid.displayName +
          '&appid=' + AppService.appId + '&id=' +
          item.itemid._id +
          '&image=' +
          imageUrl +
          item.itemid.landscapeThumbnail.imagekey +
          '&url=http://smottapp.com:3002/' + AppService.appId + '/' +
          item.itemid.urlName,
      });
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      alert(error.message);
    }
  };

  return (
    console.log(item),
    (
      <SafeAreaView style={{ flex: 1, backgroundColor: colors.primaryBackgroundColor }}>
        <Spinner
          visible={spinner}
          textContent={'Loading...'}
          textStyle={{ color: colors.primaryHighlightColor }}
          overlayColor={colors.primaryBackgroundColor}
          color={colors.primaryHighlightColor}
        />
        <ScrollView showsVerticalScrollIndicator={false} nestedScrollEnabled={true} >
          {
            item.itemid != null ?
              (
                <View style={{ alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
                  {item.itemid.mainHeader != null ? (
                    <Text
                      style={{
                        margin: width / 20,
                        fontSize: width / 20,
                        fontWeight: 'bold',
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginBottom: width / 100,
                        color: colors.primaryTextColor,
                        marginTop: 10,
                        textDecorationLine: 'underline',
                        textDecorationColor: 'red',
                      }}>
                      {item.itemid.mainHeader}
                    </Text>
                  ) : null}
                  <View style={{ flexDirection: 'row', justifyContent: isRTL ? 'flex-end' : 'flex-start' }}>
                    {item.itemid.author != null ? (
                      <Text
                        style={{
                          marginLeft: width / 22,
                          fontSize: width / 36,
                          textAlign: isRTL ? 'right' : 'left',
                          writingDirection: isRTL ? 'rtl' : 'ltr',
                          marginTop: width / 100,
                          marginBottom: width / 50,
                          color: '#808080',
                        }}>
                        {item.itemid.author}
                      </Text>
                    ) : null}
                    {item.itemid.publishDate != null ? (
                      <View style={{ flexDirection: 'row', alignItems: isRTL ? 'flex-end' : 'flex-start' }}>
                        <MaterialCommunityIcons
                          style={{
                            marginLeft: width / 38,
                            marginTop: width / 125,
                            marginBottom: width / 50,
                            color: '#808080',
                          }}
                          name="time-outline"
                          color={'#808080'}
                          size={width / 30}
                        />

                        <Text
                          style={{
                            marginLeft: 2,
                            fontSize: width / 36,
                            textAlign: isRTL ? 'right' : 'left',
                            writingDirection: isRTL ? 'rtl' : 'ltr',
                            marginTop: width / 100,
                            marginBottom: width / 50,
                            color: '#808080',
                          }}>
                          {item.itemid.publishDate}
                        </Text>
                      </View>
                    ) : null}
                    <TouchableOpacity
                      onPress={onShare}
                      style={{
                        marginLeft: isRTL ? 0 : width / 38,
                        marginRight: isRTL ? width / 38 : 0,
                        marginTop: width / 550,
                        marginBottom: width / 50,
                        color: '#808080',
                      }}>
                      <View>
                        <MaterialCommunityIcons name="share" color={'#808080'} size={width / 26} />
                      </View>
                    </TouchableOpacity>
                  </View>
                  {item.itemid.blockQuote != null && item.itemid.cite != null ? (
                    <Text
                      style={{
                        margin: width / 22,
                        marginBottom: width / 25,
                        marginTop: 10,
                      }}>
                      {item.itemid.blockQuote != null ? (
                        <Text
                          style={{
                            fontSize: width / 25,
                            fontWeight: 'bold',
                            textAlign: isRTL ? 'right' : 'left',
                            writingDirection: isRTL ? 'rtl' : 'ltr',
                            color: colors.primaryTextColor,
                          }}>
                          &lsquo;&lsquo;{item.itemid.blockQuote} &rsquo;&rsquo;{'   '}
                        </Text>
                      ) : null}
                      {item.itemid.cite != null ? (
                        <Text
                          style={{
                            fontSize: width / 35,
                            fontWeight: 'bold',
                            textAlign: isRTL ? 'right' : 'left',
                            writingDirection: isRTL ? 'rtl' : 'ltr',
                            color: colors.primaryTextColor,
                          }}>
                          {item.itemid.cite}
                        </Text>
                      ) : null}
                    </Text>
                  ) : null}
                  {item.itemid.introduction != null ? (
                    <Text
                      style={{
                        margin: width / 22,
                        fontSize: width / 30,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginBottom: width / 25,
                        color: colors.primaryTextColor,
                        marginTop: 1,
                      }}>
                      {item.itemid.introduction}
                    </Text>
                  ) : null}
                  {item.itemid.items.length == 0 ? (
                    <Image
                      PlaceholderContent={<ActivityIndicator />}
                      source={{
                        uri: imageUrl + item.itemid.bannerImage.imagekey,
                      }}
                      style={{
                        width: width,
                        height: width / 1.8,
                      }}
                    />
                  ) : (
                    <Swiper
                      style={styles.wrapper}
                      height={width / 1.77}
                      autoplay
                      marginBottom={width / 30}>
                      {item.itemid.items.map((item, key) => {
                        return (
                          <View key={key} style={styles.slide1}>
                            {item.itemid != null ? (
                              <Image
                                PlaceholderContent={<ActivityIndicator />}
                                style={styles.image}
                                source={{
                                  uri: item.itemid != null ? imageUrl + item.itemid : null,
                                }}
                              />
                            ) : null}
                          </View>
                        );
                      })}
                    </Swiper>
                  )}
                  {item.itemid.sectionHeader1 != null ? (
                    <Text
                      style={{
                        margin: width / 22,
                        fontSize: width / 30,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginBottom: width / 25,
                        color: colors.primaryTextColor,
                        marginTop: width / 25,
                      }}>
                      {item.itemid.sectionHeader1}
                    </Text>
                  ) : null}
                  {item.itemid.sectionDescription1 != null ? (
                    <Text
                      style={{
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        margin: width / 22,
                        fontSize: width / 30,
                        marginBottom: width / 25,
                        color: colors.primaryTextColor,
                        marginTop: 16,
                      }}>
                      {item.itemid.sectionDescription1}
                    </Text>
                  ) : null}
                  {/* {item.itemid.videoId != null ? (
              <JWPlayer
                videoID={item.itemid.videoId}
                image={imageUrl + item.itemid.landscapeThumbnail.imagekey}
              />
            ) : null} */}
                  {item.itemid.sectionHeader2 != null ? (
                    <Text
                      style={{
                        margin: width / 22,
                        fontSize: width / 30,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginBottom: width / 25,
                        color: colors.primaryTextColor,
                        marginTop: width / 25,
                      }}>
                      {item.itemid.sectionHeader2}
                    </Text>
                  ) : null}
                  {item.itemid.sectionDescription2 != null ? (
                    <Text
                      style={{
                        margin: width / 22,
                        fontSize: width / 30,
                        textAlign: isRTL ? 'right' : 'left',
                        writingDirection: isRTL ? 'rtl' : 'ltr',
                        marginBottom: width / 25,
                        color: colors.primaryTextColor,
                        marginTop: 1,
                      }}>
                      {item.itemid.sectionDescription2}
                    </Text>
                  ) : null}
                  {item.itemid.recommendation != null ? (
                    <LandscapeList
                      color={colors}
                      seemorecomp={''}
                      listtitle={''}
                      seemoretext={''}
                      seemoretextvis={false}
                      url={''}
                      urltype={''}
                      datas={Recommend.recommendation}
                    />
                  ) : null}
                </View>
              ) : (
                <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
                  <Text style={{
                    color: color.primaryTextColor, fontSize: 16, textAlign: isRTL ? 'right' : 'left',
                    writingDirection: isRTL ? 'rtl' : 'ltr',
                  }}>No data found</Text>
                </View>
              )
          }
        </ScrollView>
      </SafeAreaView >
    )
  );
};

export default ArticleDetailsScreen;
