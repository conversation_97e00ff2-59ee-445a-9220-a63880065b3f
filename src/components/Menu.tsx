import React, { useEffect, useState, useCallback } from 'react';
import {
  Dimensions,
  StyleSheet,
  ScrollView,
  View,
  Image,
  Text,
  TouchableOpacity,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width, height } = Dimensions.get('window');

const uri = 'https://pickaface.net/gallery/avatar/Opi51c74d0125fd4.png';

// Types for the props
interface MenuItemProps {
  icon: string;
  label: string;
  onPress: () => void;
  themeColor: { [key: string]: string };
}

interface MenuProps {
  onItemSelected: (item: string) => void;
}

interface ProfileData {
  full_name: string;
  // Add other fields as needed
}

// Reusable Menu Item Component
const MenuItem: React.FC<MenuItemProps> = ({ icon, label, onPress, themeColor, isRTL }) => (
  <TouchableOpacity onPress={onPress} style={[styles(themeColor).itemView, { justifyContent: isRTL ? 'flex-end' : 'flex-start', width: width / 1.8 }]}>
    {!isRTL && (<MaterialCommunityIcons name={icon} color={themeColor.primaryTextColor} size={width / 2} />)}
    <Text style={[styles(themeColor).item, {
      textAlign: isRTL ? 'right' : 'left',
      writingDirection: isRTL ? 'rtl' : 'ltr',
      marginRight: isRTL ? width / 25 + 10 : 0
    }]}>{label}</Text>
    {isRTL && (<MaterialCommunityIcons name={icon} color={themeColor.primaryHighlightColor} size={width / 20} />)}
  </TouchableOpacity>
);

const Menu: React.FC<MenuProps> = ({ onItemSelected }) => {
  const themeColor = useSelector((state: RootState) => state.homeScreen.colors);
  const mobilelogokey = useSelector((state: RootState) => state.homeScreen.imageKey);
  const { colors, imageUrl, imageKey, isRTL } = useSelector((state: RootState) => state.homeScreen);

  const [inplayertoken, setInplayerToken] = useState<{ token: string } | null>(null);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);

  const navigation = useNavigation();

  const fetchUserDetails = useCallback(async () => {
    if (!inplayertoken?.token) return;
    try {
      const result = await axios.get<ProfileData>('https://staging-v2.inplayer.com/accounts', {
        headers: { Authorization: inplayertoken.token },
      });
      setProfileData(result.data);
    } catch (error) {
      console.error('Failed to fetch user details:', error);
    }
  }, [inplayertoken]);

  const fetchToken = useCallback(async () => {
    try {
      const token = await AsyncStorage.getItem('inplayer_token');
      setInplayerToken(token ? JSON.parse(token) : null);
    } catch (error) {
      console.error('Failed to fetch token:', error);
    }
  }, []);



  useEffect(() => {
    fetchToken();
  }, [fetchToken]);

  useEffect(() => {
    if (!profileData) {
      fetchUserDetails();
    }
  }, [profileData, fetchUserDetails]);

  const removeToken = async () => {
    try {
      await AsyncStorage.removeItem('inplayer_token');
      setInplayerToken(null);
      setProfileData(null);
    } catch (error) {
      console.error('Failed to remove token:', error);
    }
  };

  const menuLabels = {
    login: isRTL ? 'تسجيل الدخول' : 'Login',
    changeAppId: isRTL ? 'تغيير معرف التطبيق' : 'Change App ID',
    championshipStandings: isRTL ? 'ترتيب البطولة' : 'Championship Standings',
    favourites: isRTL ? 'المفضلة' : 'Favourites',
    watchHistory: isRTL ? 'مشاهدة التاريخ' : 'Watch History',
    premium: isRTL ? 'ممتاز' : 'Premium',
    logout: isRTL ? 'تسجيل خروج' : 'Logout',
    helpFAQ: isRTL ? 'المساعدة والأسئلة الشائعة' : 'Help & FAQ',
    privacyPolicy: isRTL ? 'سياسة الخصوصية' : 'Privacy Policy',
    termsOfUse: isRTL ? 'شروط الاستخدام' : 'Terms Of Use',
    settings: isRTL ? 'الإعدادات' : 'Settings',
  };

  const menuItems = [
    { key: 'login', icon: 'login', screen: 'Login', condition: !inplayertoken },
    { key: 'changeAppId', icon: 'account-edit', screen: 'AppIdAuthScreen' },
    { key: 'championshipStandings', icon: 'trophy', screen: 'ChampionshipStandings' },
    { key: 'favourites', icon: 'star-check', screen: 'favourites' },
    { key: 'watchHistory', icon: 'history', screen: 'watchhistory' },
    { key: 'premium', icon: 'star-circle', screen: 'Login' },
    { key: 'logout', icon: 'logout', action: removeToken, condition: inplayertoken },
    { key: 'helpFAQ', icon: 'help-circle', screen: 'Login' },
    { key: 'privacyPolicy', icon: 'shield-check', screen: 'Login' },
    { key: 'termsOfUse', icon: 'text-box', screen: 'Login' },
    { key: 'settings', icon: 'cog', screen: 'Appsettings' },
  ];

  return (
    <ScrollView scrollsToTop={false} style={styles(themeColor).menu}>
      <Image
        style={styles(themeColor).logo}
        source={{ uri: `${imageUrl}${mobilelogokey}` }}
        resizeMode='contain'
      />

      {inplayertoken && profileData && (
        <View style={styles(themeColor).avatarContainer}>
          <Image style={styles(themeColor).avatar} source={{ uri }} />
          <Text style={[styles(themeColor).name, {
            textAlign: isRTL ? 'right' : 'left',
            writingDirection: isRTL ? 'rtl' : 'ltr',
          }]}>{profileData.full_name}</Text>
        </View>
      )}

      {menuItems
        .filter(item => item.condition === undefined || item.condition)
        .map(({ key, icon, screen, action }) => (
          <MenuItem
            key={key}
            icon={icon}
            label={menuLabels[key]}
            onPress={() => {
              onItemSelected(key);
              action ? action() : navigation.navigate(screen, { color: themeColor });
            }}
            themeColor={themeColor}
            isRTL={isRTL}
          />
        ))}

      <View style={styles(themeColor).separator} />
    </ScrollView>
  );
}

export default Menu;

const styles = (themeColor) =>
  StyleSheet.create({
    menu: {
      flex: 1,
      width,
      height,
      backgroundColor: themeColor.secondaryBackgroundColor,
      padding: 20,
    },
    avatarContainer: {
      marginBottom: 20,
      marginTop: 20,
    },
    avatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
    },
    name: {
      position: 'absolute',
      left: 70,
      top: 20,
      fontWeight: 'bold',
      color: themeColor.primaryTextColor,
    },
    item: {
      fontSize: width / 28,
      left: width / 20,
      fontWeight: 'bold',
      color: themeColor.primaryTextColor,
    },
    itemView: {
      flexDirection: 'row',
      paddingTop: width / 20,
    },
    separator: {
      height: 1,
      backgroundColor: themeColor.primaryTextColor,
      marginVertical: width / 20,
    },
    logo: {
      width: width / 3,
      height: width / 20 + 50,
      marginTop: Platform.OS === 'ios' ? 50 : 0,
      marginLeft: width / 8.6,
    },
  });
