import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Text,
  Dimensions,
  View,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { PlaylistItem } from './FullLandscapeListImageViewDouble';
import { Image } from 'react-native-elements';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';

// Define types for props
interface NewsLandscapeProps {
  datas: {
    playlistId: string;
    items: Array<{
      itemid: {
        landscapeThumbnail: {
          imagekey: string;
        };
        pageType: string;
        externalPage: string;
        urlName: string;
        tag: string;
        mainHeader: string;
        devices: { itemName: string; itemVisibility: boolean }[];
      };
    }>;
  };
  color: string;
  listtitle: string;
  seemoretext: string;
  seemoretextvis: boolean;
  seemorecomp: string;
  appsettings?: {
    imageBorder?: boolean;
  };
}

// Define types for FullData
interface FullDataType {
  imageUrl: string;
  data: any; // Can be further refined based on API structure
  pageType: string;
  itemnavigation: string;
  urlname: string;
  tag: string;
  mainHeader: string;
}

const { width } = Dimensions.get('window');

const NewsLandscape: React.FC<NewsLandscapeProps> = (props) => {
  const navigation = useNavigation();
  const colors = useSelector((state: RootState) => state.homeScreen.colors);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);
  const [JwData, setJwData] = useState<unknown[]>([]); // You can type this further based on API response
  const FullData: FullDataType[] = [];
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);

  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [NewsLandscape]:', error);
    }
  }


  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.forEach((item, key) => {
        FullData.push({
          imageUrl: imageUrl + (item.itemid.landscapeThumbnail != null ? item.itemid.landscapeThumbnail.imagekey : ""),
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          tag: item.itemid.tag,
          mainHeader: item.itemid.mainHeader,
        });
      });
    }

    if (JwData.playlist != null) {
      JwData.playlist.forEach((item, key) => {
        FullData.push({
          imageUrl: isYoutubeVideo ? item.thumbnail : item.image,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.MediaType,
          mainHeader: item.title,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
    console.log(FullData, 'FullData');
    console.log(youtubeData, 'youtubeData');

  }

  // Navigation logic for different page types
  function handleNavigationTo(pageType, itemnavigation, itemdata, urlname) {
    if (pageType === 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType === 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType === 'videoPage') {
      if (isYoutubeVideo) {
        navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType === 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }



  return (
    setPlaylistData(),
    (
      < View style={{ marginBottom: width / 25, marginTop: width / 60 }
      }>
        <View style={styles(colors, isRTL, props).row}>
          {
            props.seemoretextvis && props.seemoretext &&
            (
              isRTL && (
                <View style={styles(colors, isRTL, props).inputWrap}>
                  <TouchableOpacity
                    style={styles(colors, isRTL, props).text2}
                    onPress={() => {
                      navigation.navigate('SeeMore', {
                        itemId: props.listtitle,
                        listdatas: props.datas,
                        comp: props.seemorecomp,
                        color: props.color,
                      });
                    }}>
                    <Text style={styles(colors, isRTL, props).text2}>{props.seemoretext}</Text>
                  </TouchableOpacity>
                </View>
              )
            )}
          <Text style={styles(colors, isRTL, props).text1}>{props.listtitle}</Text>
          {/* <View style={styles(colors, isRTL, props).inputWrap}></View> */}
          {
            props.seemoretextvis && props.seemoretext &&
            (
              !isRTL && (
                <View style={styles(colors, isRTL, props).inputWrap}>
                  <TouchableOpacity
                    style={styles(colors, isRTL, props).text2}
                    onPress={() => {
                      navigation.navigate('SeeMore', {
                        itemId: props.listtitle,
                        listdatas: props.datas,
                        comp: props.seemorecomp,
                        color: props.color,
                      });
                    }}>
                    <Text style={styles(colors, isRTL, props).text2}>{props.seemoretext}</Text>
                  </TouchableOpacity>
                </View>
              )
            )}
        </View>

        <FlatList
          style={{ marginTop: width / 50, direction: isRTL ? 'rtl' : 'ltr' }}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          data={FullData}
          ItemSeparatorComponent={() => <View style={{ width: 16 }} />} // horizontal space between cards
          contentContainerStyle={{
            paddingVertical: 16,
            alignItems: 'center',
          }}
          renderItem={({ item, index }) => (
            item.imageUrl ? (
              <View style={cardStyles.cardContainer}>
                <View style={cardStyles.imageWrapper}>
                  <Image
                    PlaceholderContent={<ActivityIndicator />}
                    source={{ uri: item.imageUrl }}
                    style={cardStyles.image}
                  />
                </View>
                <View style={cardStyles.bottomContainer}>
                  <Text style={cardStyles.headerText}>{item.mainHeader}</Text>
                  <TouchableOpacity style={cardStyles.playButton} onPress={() => handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname)}>
                    <FontAwesome5 name="play" size={16} color="#007AFF" />
                  </TouchableOpacity>
                </View>
              </View>
            ) : null
          )}
        />
      </View >
    )

  );
};

export default NewsLandscape;



const styles = (colors, isRTL, props) => StyleSheet.create({
  row: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: isRTL ? 'flex-end' : 'flex-start'
  },
  inputWrap: {
    flex: 1,
    borderColor: colors.primaryTextColor,
  },
  text1: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    // left: width / 40,
    marginHorizontal: width / 40,
  },
  text2: {
    color: colors.primaryTextColor,
    fontSize: width / 30,
    fontWeight: 'bold',
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    marginHorizontal: width / 40,
  },
  text3: {
    color: colors.elementForegroundColor,
    position: 'absolute',
    fontWeight: 'bold',
    fontSize: width / 30,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    maxHeight: width / 10,
    width: width / 1.5,
    bottom: width / 100,
  },
  text4: {
    fontWeight: 'bold',
    fontSize: width / 34,
    color: colors.elementForegroundColor,
    textAlign: isRTL ? 'right' : 'left',
    writingDirection: isRTL ? 'rtl' : 'ltr',
    height: width / 20,
    textTransform: 'uppercase',
    top: 2,
  },
  textview: {
    color: colors.elementForegroundColor,
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: colors.secondaryHighlightColor,
    borderTopLeftRadius: props.appsettings?.imageBorder === false ? width / 50 : 0,
    borderWidth: 0,
    borderColor: '#fff',
  },
});

const cardStyles = StyleSheet.create({
  cardContainer: {
    width: width-20,
    backgroundColor: '#fff',
    borderRadius: 16,
    alignItems: 'center',
    alignContent: 'center',
    alignSelf: 'center',
    shadowRadius: 6,
    marginBottom: 8,
    overflow: 'hidden',
    marginLeft: 10,
  },
  imageWrapper: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    paddingTop: 16,
    paddingBottom: 8,
  },
  image: {
    width: width - 50,
    height: width / 2.5,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  bottomContainer: {
    width: '100%',
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  headerText: {
    color: '#222',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  playButton: {
    backgroundColor: '#fff',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
    borderWidth: 2,
    borderColor: '#000',
  },
  playButtonText: {
    alignSelf: 'center',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#007AFF',
    fontSize: 22,
    fontWeight: 'bold',
  },
});
