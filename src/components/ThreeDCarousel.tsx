import React from 'react';
import { Component, useState, useEffect } from 'react';
import axios from 'axios';
import { Text, View, StyleSheet, Dimensions, TouchableOpacity, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import { scrollInterpolator, animatedStyles } from '../utils/animation2';
import { ActivityIndicator } from 'react-native';
import { Image } from 'react-native-elements';
import constant from '../config/constant';
import { VideoPlayerService } from '../services/VideoPlayerService';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const ThreeDCarousal = (props) => {
  const [index, setIndex] = React.useState(1);
  const isCarousel = React.useRef(null);
  const navigation = useNavigation();
  const SLIDER_WIDTH = Dimensions.get('window').width;
  const ITEM_WIDTH = Math.round(SLIDER_WIDTH * 0.8);
  const ITEM_HEIGHT = Math.round((ITEM_WIDTH * 3) / 4);
  const [jwData, setJwData] = useState([]);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [ThreeDCarousal]:', error);
    }
  }


  function setPlayListData() {
    {
      if (props.datas != null) {
        props.datas.items.map((item, key) => {
          FullData.push({
            imageUrl: constant.imageurl + item.itemid.landscapeThumbnail.imagekey,
            data: item,
            pageType: item.itemid.pageType,
            itemnavigation: item.itemid.externalPage,
            urlname: item.itemid.urlName,
            displayname: item.itemid.displayName,
            introduction: item.itemid.introduction,
          });
        });
      }
      if (jwData.playlist != null) {
        jwData.playlist.forEach((item, key) => {
          FullData.push({
            imageUrl: item.image,
            data: item.title,
            pageType: 'videoPage',
            itemnavigation: '',
            urlname: '',
            displayname: item.title,
            introduction: item.description,
          });
        });
      }
    }
    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  function handleNavigationTo(pageType, itemnavigation, itemdata, urlname) {
    if (pageType == 'externalPage') {
      return Linking.openURL(itemnavigation);
    } else if (pageType == 'articlePage') {
      return navigation.push('ArticleDetails', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'seasonPage') {
      return navigation.push('Season', {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == 'videoPage') {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType == 'normalPage') {
      return navigation.push('MoreDetail', {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  const CarouselCardItem = ({ item, index }) => {
    return (
      <View key={index} style={styles(ITEM_WIDTH, width * 0.565).slide1}>
        <TouchableOpacity
          // {item.data.itemid.devices[2].itemName == 'iOS' &&
          // item.data.itemid.devices[2].itemVisibility == true?
          // onPress={() => {
          //   navigationlist(
          //     item.pageType,
          //     item.itemnavigation,
          //     item.data,
          //     item.urlname,
          //   );
          // }}>:
          // onPress={() => {
          //   navigation.push('pagenotfound', {});
          // }}></View>}
          onPress={() => {
            const hasItemId = item.data.itemid !== undefined;
            const isIOSDevice = hasItemId && item.data.itemid.devices[2].itemName === 'iOS';
            const isVisible = hasItemId && item.data.itemid.devices[2].itemVisibility === true;

            if ((isIOSDevice && isVisible) || !hasItemId) {
              handleNavigationTo(item.pageType, item.itemnavigation, item.data, item.urlname);
            } else {
              navigation.push('pagenotfound', {
                color: props.color,
              });
            }
          }}>
          {item.imageUrl != null ? (
            <Image
              PlaceholderContent={<ActivityIndicator />}
              source={{
                uri: item.imageUrl != null ? item.imageUrl : null,
              }}
              style={styles(ITEM_WIDTH, ITEM_HEIGHT).image}
              resizeMode='contain'

            />
          ) : null}
        </TouchableOpacity>
      </View>
    );
  };

  return (
    setPlayListData(),
    (
      <View style={{ width: width, height: width * 0.5, marginTop: -10 }}>
        <Carousel
          firstItem={FullData.length > 1 ? 1 : 0}
          initialScrollIndex={FullData.length > 1 ? 1 : 0}
          ref={isCarousel}
          data={FullData}
          renderItem={CarouselCardItem}
          sliderWidth={SLIDER_WIDTH}
          itemWidth={ITEM_WIDTH}
          containerCustomStyle={styles(ITEM_WIDTH, ITEM_HEIGHT).carouselContainer}
          inactiveSlideShift={0}
          onSnapToItem={(index) => setIndex(index)}
          scrollInterpolator={scrollInterpolator}
          slideInterpolatedStyle={animatedStyles}
          useScrollView={true}
          inactiveSlideScale={1} // Adjust scale for inactive items
          inactiveSlideOpacity={1} // allows each slide to fill to the entire width
        />
        {/* <View style={{ marginTop: width / -15, width: '100%', alignItems: 'center' }}>
          <Pagination
            dotsLength={FullData.length}
            activeDotIndex={index}
            carouselRef={isCarousel}
            dotStyle={{
              width: 20,
              height: 5,
              borderRadius: 5,
              marginHorizontal: -8,
              backgroundColor: 'rgba(0, 0, 0, 0.92)',
            }}
            inactiveDotOpacity={0.4}
            inactiveDotScale={0.6}
            tappableDots={true}
          />
        </View> */}
      </View>
    )
  );
};
export default ThreeDCarousal;


const styles = (ITEM_WIDTH, ITEM_HEIGHT) => StyleSheet.create({
  wrapper: {},
  slide1: {
    // width: width,
    // height: '110%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold',
  },
  image: {
    // backgroundColor: 'white',
    // aspectRatio: 16 / 9,
    width: width / 1.28,
    height: '100%',

  },
  carouselContainer: {
    // marginTop: 20,
  },
});