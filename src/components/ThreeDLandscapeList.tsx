import React from "react";
import { useEffect, useState } from "react";
import axios from "axios";
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Linking,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import Carousel from "react-native-snap-carousel";
import { scrollInterpolator, animatedStyles } from "../utils/animations";
import { ActivityIndicator } from "react-native";
import { Image } from "react-native-elements";
import { useSelector } from "react-redux";
import { RootState } from "../redux/store";
import { VideoPlayerService } from "../services/VideoPlayerService";
import { PlaylistItem } from "./FullLandscapeListImageViewDouble";

const { width } = Dimensions.get("window");

const ThreeDLandscapeList = (props) => {
  const [index, setIndex] = React.useState(0);
  const imageUrl = useSelector((state: RootState) => state.homeScreen.imageUrl);

  const isCarousel = React.useRef(null);
  const navigation = useNavigation();
  const SLIDER_WIDTH = Dimensions.get("window").width;
  const ITEM_WIDTH = Math.round(SLIDER_WIDTH * 0.8);
  const ITEM_HEIGHT = Math.round((ITEM_WIDTH * 3) / 4);
  const videoInfo = useSelector((state: RootState) => state.homeScreen.videoCloud);
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const [jwData, setJwData] = useState<unknown[]>([]);
  const [youtubeData, setYoutubeData] = useState<unknown[]>([]);
  const [isYoutubeVideo, setIsYoutubeVideo] = useState(false);
  const FullData = [];


  useEffect(() => {
    initVideoInfo();
  }, []);

  const initVideoInfo = async () => {
    try {
      if (!props.datas.playlistId) {
        return;
      }

      if (videoInfo[0].cloudName === 'YouTube') {
        const videoData = await VideoPlayerService.fetchYoutubePlaylist(props.datas.playlistId);
        setYoutubeData(videoData);
        setIsYoutubeVideo(true);
      } else {
        const videoData = await VideoPlayerService.fetchJwplayerPlaylist(props.datas.playlistId);
        setJwData(videoData);
      }
    } catch (error) {
      console.error('Error fetching playlist [ThreeDLandscapeList]:', error);
    }
  };


  function handleNavigationTo(pageType, itemnavigation, itemdata, urlname) {
    if (pageType == "externalPage") {
      return Linking.openURL(itemnavigation);
    } else if (pageType == "articlePage") {
      return navigation.push("ArticleDetails", {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == "seasonPage") {
      return navigation.push("Season", {
        item: itemdata,
        color: props.color,
      });
    } else if (pageType == "videoPage") {
      if (isYoutubeVideo) {
        return navigation.push('YouTubeDetailsScreen', { item: itemdata, color: props.color, });
      } else {
        return navigation.push('Details', { item: itemdata, color: props.color, });
      }
    } else if (pageType == "normalPage") {
      return navigation.push("MoreDetail", {
        itemurlname: urlname,
        color: props.color,
      });
    }
  }

  const CarouselCardItem = ({ item, index }) => {
    return (
      <View key={index} style={styles(ITEM_WIDTH, ITEM_HEIGHT).slide1}>
        <TouchableOpacity
          onPress={() => {
            const hasItemId = item.data.itemid !== undefined;
            const isIOSDevice = hasItemId && item.data.itemid.devices[2].itemName === "iOS";
            const isVisible = hasItemId && item.data.itemid.devices[2].itemVisibility === true;

            if ((isIOSDevice && isVisible) || !hasItemId) {
              handleNavigationTo(
                item.pageType,
                item.itemnavigation,
                item.data,
                item.urlname
              );
            } else {
              navigation.push("pagenotfound", {
                color: props.color,
              });
            }
          }}
        >
          {item.imageUrl != null ? (
            <Image
              PlaceholderContent={<ActivityIndicator />}
              source={{
                uri: item.imageUrl != null ? item.imageUrl : null,
              }}
              style={styles(ITEM_WIDTH, ITEM_HEIGHT).image}
            />
          ) : null}
        </TouchableOpacity>
      </View>
    );
  };

  function setPlaylistData() {
    if (props.datas != null) {
      props.datas.items.map((item, key) => {
        FullData.push({
          imageUrl: imageUrl + item.itemid.landscapeThumbnail.imagekey,
          data: item,
          pageType: item.itemid.pageType,
          itemnavigation: item.itemid.externalPage,
          urlname: item.itemid.urlName,
          displayname: item.itemid.displayName,
          introduction: item.itemid.introduction,
        });
      });
    }
    if (jwData.playlist != null) {
      jwData.playlist.forEach((item, key) => {
        FullData.push({
          imageUrl: item.image,
          data: item,
          pageType: "videoPage",
          itemnavigation: "",
          urlname: "",
          displayname: item.title,
          introduction: item.description,
        });
      });
    }

    if (youtubeData != null) {
      youtubeData.forEach((item, key) => {
        FullData.push({
          imageUrl: item.thumbnail,
          data: item,
          pageType: 'videoPage',
          itemnavigation: '',
          urlname: '',
          tag: item.etag,
          mainHeader: item.title,
        });
      });
    }
  }

  return (
    setPlaylistData(),
    (
      <View>
        <Carousel
          ref={isCarousel}
          data={FullData}
          renderItem={CarouselCardItem}
          sliderWidth={SLIDER_WIDTH}
          itemWidth={ITEM_WIDTH}
          containerCustomStyle={styles(ITEM_WIDTH, ITEM_HEIGHT).carouselContainer}
          inactiveSlideShift={0}
          onSnapToItem={(index) => setIndex(index)}
          scrollInterpolator={scrollInterpolator}
          slideInterpolatedStyle={animatedStyles}
          useScrollView={true}
        />
      </View>
    )
  );
};
export default ThreeDLandscapeList;

const styles = (ITEM_WIDTH, ITEM_HEIGHT) => StyleSheet.create({
  wrapper: {},
  slide1: {
    width: ITEM_WIDTH,
    height: ITEM_HEIGHT,
    alignItems: "center",
    justifyContent: "center",
  },
  text: {
    color: "#fff",
    fontSize: 30,
    fontWeight: "bold",
  },
  image: {
    width: ITEM_WIDTH,
    height: width / 2,
  },
  carouselContainer: {
    marginTop: 20,
  },
});