import React from 'react';
import { FlatList, Text, Dimensions, View, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';

const { width } = Dimensions.get('window');

const RoundList = (props) => {
  const { isRTL } = useSelector((state: RootState) => state.homeScreen);
  const CData = [];

  const navigation = useNavigation();

  const styles = (isRTL: boolean) => StyleSheet.create({
    row: {
      flex: 1,
      flexDirection: 'row',
    },
    inputWrap: {
      flex: 1,
      borderColor: props.color.primaryTextColor,
    },
    text1: {
      color: props.color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      left: width / 40,
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

    },
    text2: {
      color: props.color.primaryTextColor,
      fontSize: width / 30,
      fontWeight: 'bold',
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',

      right: width / 40,
    },
    text3: {
      color: props.color.primaryTextColor,
      fontSize: width / 40,
      textAlign: 'center',
      textAlign: isRTL ? 'right' : 'left', writingDirection: isRTL ? 'rtl' : 'ltr',
    },
    text4: {
      color: props.color.primaryTextColor,
      fontSize: width / 35,
      fontWeight: 'bold',
      textAlign: 'center',
    },
  });

  const setData = () => {
    if (props.datas.Actor != '' && props.datas.Actor != null) {
      CData.push({ title: 'Starring', name: props.datas.Actor });
    }
    if (props.datas.Director != '' && props.datas.Director != null) {
      CData.push({ title: 'Director', name: props.datas.Director });
    }
    if (props.datas.Producer != '' && props.datas.Producer != null) {
      CData.push({ title: 'Producer', name: props.datas.Producer });
    }
    if (props.datas.Writers != '' && props.datas.Writers != null) {
      CData.push({ title: 'Writer', name: props.datas.Writers });
    }
    if (props.datas.ProductionStudio != '' && props.datas.ProductionStudio != null) {
      CData.push({ title: 'ProductionStudio', name: props.datas.ProductionStudio });
    }
    if (props.datas.starring != '' && props.datas.starring != null) {
      CData.push({ title: 'Starring', name: props.datas.starring });
    }
    if (props.datas.director != '' && props.datas.director != null) {
      CData.push({ title: 'Director', name: props.datas.director });
    }
    if (props.datas.producer != '' && props.datas.producer != null) {
      CData.push({ title: 'Producer', name: props.datas.producer });
    }
    if (props.datas.writer != '' && props.datas.writer != null) {
      CData.push({ title: 'Writer', name: props.datas.writer });
    }
    if (props.datas.studio != '' && props.datas.studio != null) {
      CData.push({ title: 'ProductionStudio', name: props.datas.studio });
    }
  };

  return (
    setData(),
    CData.length != 0 ? (
      <View style={{ marginBottom: width / -15 }}>
        <View style={styles(isRTL).row}>
          <View style={styles(isRTL).inputWrap}>
            <Text style={styles(isRTL).text1}>{props.listtitle}</Text>
          </View>
          <View style={styles(isRTL).inputWrap}>
            {props.seemoretextvis == true ? (
              <TouchableOpacity
                style={styles(isRTL).text2}
                onPress={() => {
                  navigation.navigate('SeeMore', {
                    itemId: props.listtitle,
                    listdatas: props.datas,
                    comp: props.seemorecomp,
                    color: props.color,
                  });
                }}>
                <Text style={styles(isRTL).text2}>{props.seemoretext}</Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <View style={{ marginBottom: width / 100 }}>
          <FlatList
            style={{ marginTop: width / 20 }}
            numColumns={3}
            scrollEnabled={false}
            showsHorizontalScrollIndicator={false}
            data={CData}
            renderItem={({ item }) => (
              <View
                style={{
                  marginLeft: width / 35,
                  marginRight: width / 300,
                  alignItems: 'center',
                  width: width / 3.4,
                  height: width / 5,
                }}>
                <Text style={styles(isRTL).text4}>{item.title}</Text>
                <Text style={styles(isRTL).text3}>{item.name}</Text>
              </View>
            )}
          />
        </View>
      </View>
    ) : null
  );
};

export default RoundList;
