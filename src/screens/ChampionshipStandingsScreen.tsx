import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Modal,
  Platform,
} from "react-native";
import { useSelector } from "react-redux";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { RootState } from "../redux/store";
import DateTimePicker from "@react-native-community/datetimepicker";

const { width } = Dimensions.get("window");

interface StandingItem {
  id: number;
  name: string;
  initials: string;
  strikeRate: number;
  totalPrize: number;
  rides: number;
  first: number;
  second: number;
  third: number;
  fourth: number;
  rank: number;
}

interface HorseRaceEntry {
  saddleNo: number;
  horseName: string;
  horseDescription: string;
  draw: number;
  owner: string;
  trainer: string;
  jockey: string;
  weight: number;
  rank?: number;
}

const ChampionshipStandingsScreen = ({ navigation, route }: any) => {
  const colors = useSelector((state: RootState) => state.homeScreen.colors);

  // Get the selected tab from navigation params, default to 'OWNERS'
  const selectedTabFromParams = route?.params?.selectedTab || "OWNERS";
  const [activeTab, setActiveTab] = useState(selectedTabFromParams);
  const [selectedYear, setSelectedYear] = useState("2024/2025");
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Update active tab when navigation params change
  useEffect(() => {
    if (route?.params?.selectedTab) {
      setActiveTab(route.params.selectedTab);
    }
  }, [route?.params?.selectedTab]);

  // Date picker handlers
  const handleDateChange = (event: any, date?: Date) => {
    if (Platform.OS === "android") {
      setShowDatePicker(false);
    }
    if (date) {
      setSelectedDate(date);
      const year = date.getFullYear();
      const nextYear = year + 1;
      setSelectedYear(`${year}/${nextYear}`);
    }
  };

  const showDatePickerModal = () => {
    setShowDatePicker(true);
  };

  // Function to get current data based on active tab
  const getCurrentData = () => {
    switch (activeTab) {
      case "TRAINERS":
        return trainersData;
      case "JOCKEYS":
        return jockeysData;
      default:
        return ownersData;
    }
  };

  // Function to get header text based on active tab
  const getHeaderText = () => {
    switch (activeTab) {
      case "TRAINERS":
        return "TRAINER'S NAME";
      case "JOCKEYS":
        return "JOCKEY'S NAME";
      default:
        return "OWNER'S NAME";
    }
  };

  // Horse race entries data
  const horseRaceEntries: HorseRaceEntry[] = [
    {
      saddleNo: 8,
      horseName: "Waqif (IRE)",
      horseDescription: "4yrs Bay Colt 29 (1-3-1) QR 104,750",
      draw: 3,
      owner: "Mr. Hassan Bin Ali Hassan Al Matwi",
      trainer: "Mohammed Hassan Al Matwi",
      jockey: "Saleh Faraj Al-Otaibi* (-2.5kg)",
      weight: 58.0,
      rank: 8,
    },
    // Add more entries as needed
  ];

  // Mock data - replace with actual data from your API
  const ownersData: StandingItem[] = [
    {
      id: 1,
      name: "Yas Racing",
      initials: "YR",
      strikeRate: 9.6,
      totalPrize: 4555570,
      rides: 481,
      first: 46,
      second: 64,
      third: 59,
      fourth: 47,
      rank: 1,
    },
    {
      id: 2,
      name: "Al Rashid Stables",
      initials: "AR",
      strikeRate: 8.8,
      totalPrize: 3245890,
      rides: 398,
      first: 35,
      second: 52,
      third: 48,
      fourth: 41,
      rank: 2,
    },
    {
      id: 3,
      name: "Khalid Khalifa Al Nabooda",
      initials: "KN",
      strikeRate: 9.2,
      totalPrize: 2157890,
      rides: 336,
      first: 31,
      second: 35,
      third: 33,
      fourth: 28,
      rank: 3,
    },
    {
      id: 4,
      name: "Godolphin",
      initials: "GD",
      strikeRate: 8.5,
      totalPrize: 1864520,
      rides: 291,
      first: 20,
      second: 15,
      third: 17,
      fourth: 19,
      rank: 4,
    },
    {
      id: 5,
      name: "Al Asayl Stables",
      initials: "AS",
      strikeRate: 7.5,
      totalPrize: 1456780,
      rides: 267,
      first: 18,
      second: 22,
      third: 15,
      fourth: 16,
      rank: 5,
    },
    {
      id: 6,
      name: "Emirates Racing Authority",
      initials: "ER",
      strikeRate: 6.9,
      totalPrize: 1234560,
      rides: 245,
      first: 15,
      second: 18,
      third: 14,
      fourth: 12,
      rank: 6,
    },
    {
      id: 7,
      name: "Al Naboodah Group",
      initials: "AN",
      strikeRate: 6.2,
      totalPrize: 1098750,
      rides: 223,
      first: 12,
      second: 16,
      third: 13,
      fourth: 11,
      rank: 7,
    },
    {
      id: 8,
      name: "Sh Hamdan bin Mohammed",
      initials: "SM",
      strikeRate: 5.8,
      totalPrize: 987650,
      rides: 201,
      first: 10,
      second: 14,
      third: 12,
      fourth: 9,
      rank: 8,
    },
  ];

  // Mock data for trainers championship standings
  const trainersData: StandingItem[] = [
    {
      id: 1,
      name: "Charlie Appleby",
      initials: "CA",
      strikeRate: 32.1,
      totalPrize: 2850000,
      rides: 162,
      first: 52,
      second: 28,
      third: 21,
      fourth: 18,
      rank: 1,
    },
    {
      id: 2,
      name: "Saeed bin Suroor",
      initials: "SS",
      strikeRate: 26.8,
      totalPrize: 2350000,
      rides: 153,
      first: 41,
      second: 25,
      third: 19,
      fourth: 16,
      rank: 2,
    },
    {
      id: 3,
      name: "John Gosden",
      initials: "JG",
      strikeRate: 24.5,
      totalPrize: 2050000,
      rides: 143,
      first: 35,
      second: 22,
      third: 18,
      fourth: 15,
      rank: 3,
    },
    {
      id: 4,
      name: "Aidan O'Brien",
      initials: "AO",
      strikeRate: 22.3,
      totalPrize: 1850000,
      rides: 134,
      first: 30,
      second: 20,
      third: 16,
      fourth: 14,
      rank: 4,
    },
    {
      id: 5,
      name: "William Haggas",
      initials: "WH",
      strikeRate: 20.1,
      totalPrize: 1650000,
      rides: 124,
      first: 25,
      second: 18,
      third: 15,
      fourth: 12,
      rank: 5,
    },
    {
      id: 6,
      name: "Mark Johnston",
      initials: "MJ",
      strikeRate: 18.7,
      totalPrize: 1450000,
      rides: 115,
      first: 22,
      second: 16,
      third: 13,
      fourth: 11,
      rank: 6,
    },
    {
      id: 7,
      name: "Roger Charlton",
      initials: "RC",
      strikeRate: 17.2,
      totalPrize: 1250000,
      rides: 105,
      first: 18,
      second: 14,
      third: 12,
      fourth: 10,
      rank: 7,
    },
    {
      id: 8,
      name: "Andrew Balding",
      initials: "AB",
      strikeRate: 15.8,
      totalPrize: 1150000,
      rides: 95,
      first: 15,
      second: 12,
      third: 10,
      fourth: 8,
      rank: 8,
    },
  ];

  // Mock data for jockeys championship standings
  const jockeysData: StandingItem[] = [
    {
      id: 1,
      name: "William Buick",
      initials: "WB",
      strikeRate: 35.2,
      totalPrize: 3250000,
      rides: 193,
      first: 68,
      second: 32,
      third: 24,
      fourth: 19,
      rank: 1,
    },
    {
      id: 2,
      name: "Frankie Dettori",
      initials: "FD",
      strikeRate: 29.8,
      totalPrize: 2850000,
      rides: 185,
      first: 55,
      second: 28,
      third: 22,
      fourth: 18,
      rank: 2,
    },
    {
      id: 3,
      name: "Ryan Moore",
      initials: "RM",
      strikeRate: 27.1,
      totalPrize: 2550000,
      rides: 177,
      first: 48,
      second: 26,
      third: 20,
      fourth: 16,
      rank: 3,
    },
    {
      id: 4,
      name: "James Doyle",
      initials: "JD",
      strikeRate: 25.4,
      totalPrize: 2250000,
      rides: 165,
      first: 42,
      second: 24,
      third: 18,
      fourth: 15,
      rank: 4,
    },
    {
      id: 5,
      name: "Oisin Murphy",
      initials: "OM",
      strikeRate: 23.8,
      totalPrize: 2050000,
      rides: 158,
      first: 38,
      second: 22,
      third: 16,
      fourth: 14,
      rank: 5,
    },
    {
      id: 6,
      name: "Tom Marquand",
      initials: "TM",
      strikeRate: 22.1,
      totalPrize: 1850000,
      rides: 145,
      first: 32,
      second: 20,
      third: 15,
      fourth: 12,
      rank: 6,
    },
    {
      id: 7,
      name: "Hollie Doyle",
      initials: "HD",
      strikeRate: 20.5,
      totalPrize: 1650000,
      rides: 135,
      first: 28,
      second: 18,
      third: 14,
      fourth: 11,
      rank: 7,
    },
    {
      id: 8,
      name: "Jim Crowley",
      initials: "JC",
      strikeRate: 19.2,
      totalPrize: 1450000,
      rides: 125,
      first: 24,
      second: 16,
      third: 12,
      fourth: 10,
      rank: 8,
    },
  ];

  const renderTopCard = (item: StandingItem) => (
    <View
      key={item.id}
      style={[styles.topCard, { backgroundColor: getCardColor(item.rank) }]}
    >
      <View
        style={[
          styles.rankBadge,
          { backgroundColor: getRankBgColor(item.rank) },
        ]}
      >
        <Text style={styles.rankText}>{item.rank}</Text>
      </View>

      <View style={styles.cardHeader}>
        <View style={styles.initialsCircle}>
          <Text style={styles.initialsText}>{item.initials}</Text>
        </View>
        <View style={styles.ownerInfo}>
          <Text style={styles.ownerName}>{item.name}</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>STRIKE RATE</Text>
              <Text style={styles.statValue}>{item.strikeRate} %</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>TOTAL PRIZE (AED)</Text>
              <Text style={styles.statValue}>
                {item.totalPrize.toLocaleString()}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.bottomStats}>
        <View style={styles.chartContainer}>
          <Text style={styles.ridesCount}>{item.rides}</Text>
          <View
            style={[
              styles.ridesBar,
              { height: Math.max(item.rides / 2, 30), maxHeight: 120 },
            ]}
          />
          <Text style={styles.ridesLabel}>RIDES</Text>
        </View>
        <View style={styles.positionStats}>
          <View style={styles.chartContainer}>
            <Text style={styles.positionNumber}>{item.first}</Text>
            <View
              style={[
                styles.positionBar,
                {
                  height: Math.max(
                    (item.first / item.rides) * Math.max(item.rides / 2, 30),
                    15
                  ),
                  maxHeight: 120,
                },
              ]}
            />
            <Text style={styles.positionLabel}>1ST</Text>
          </View>
          <View style={styles.chartContainer}>
            <Text style={styles.positionNumber}>{item.second}</Text>
            <View
              style={[
                styles.positionBar,
                {
                  height: Math.max(
                    (item.second / item.rides) * Math.max(item.rides / 2, 30),
                    15
                  ),
                  maxHeight: 120,
                },
              ]}
            />
            <Text style={styles.positionLabel}>2ND</Text>
          </View>
          <View style={styles.chartContainer}>
            <Text style={styles.positionNumber}>{item.third}</Text>
            <View
              style={[
                styles.positionBar,
                {
                  height: Math.max(
                    (item.third / item.rides) * Math.max(item.rides / 2, 30),
                    15
                  ),
                  maxHeight: 120,
                },
              ]}
            />
            <Text style={styles.positionLabel}>3RD</Text>
          </View>
          <View style={styles.chartContainer}>
            <Text style={styles.positionNumber}>{item.fourth}</Text>
            <View
              style={[
                styles.positionBar,
                {
                  height: Math.max(
                    (item.fourth / item.rides) * Math.max(item.rides / 2, 30),
                    15
                  ),
                  maxHeight: 120,
                },
              ]}
            />
            <Text style={styles.positionLabel}>4TH</Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderDetailedItem = (item: StandingItem) => (
    <View key={item.id} style={styles.detailedItem}>
      <View style={styles.detailedHeader}>
        <View style={[styles.rankCircle, {backgroundColor: colors.primaryHighlightColor}]}>
          <Text style={styles.rankCircleText}>{item.rank}</Text>
        </View>
        <View style={styles.initialsCircleSmall}>
          <Text style={styles.initialsTextSmall}>{item.initials}</Text>
        </View>
        <Text style={styles.detailedName}>{item.name}</Text>
      </View>

      <View style={styles.detailedStats}>
        <Text style={styles.detailedStatsText}>
          <Text style={styles.statBold}>1ST</Text> {item.first}{" "}
          <Text style={styles.statBold}>2ND</Text> {item.second}{" "}
          <Text style={styles.statBold}>3RD</Text> {item.third}{" "}
          <Text style={styles.statBold}>4TH</Text> {item.fourth}{" "}
          <Text style={styles.statBold}>R</Text> {item.rides}
        </Text>
        <Text style={styles.detailedStatsText}>
          <Text style={styles.statBold}>SR (%)</Text> {item.strikeRate}{" "}
          <Text style={styles.statBold}>TP AED</Text>{" "}
          {item.totalPrize.toLocaleString()}
        </Text>
      </View>
    </View>
  );

 const getCardColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "#F4B942"; // Gold
      case 2:
        return "#C0C0C0"; // Silver
      case 3:
        return "#CD7F32"; // Bronze
      default:
        return "#E0E0E0"; // Gray
    }
  };

  const getRankBgColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "#FFEBBC";
      case 2:
        return "#E3E3E3";
      case 3:
        return "#FFEBBC";
      default:
        return "#FFEBBC";
    }
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: 'white' },
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialCommunityIcons
            name="chevron-left"
            size={24}
            color={colors.primaryTextColor}
          />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.primaryTextColor }]}>
          CHAMPIONSHIP STANDINGS
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {["OWNERS", "TRAINERS", "JOCKEYS"].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              activeTab === tab ? {backgroundColor: colors.primaryHighlightColor} : styles.inactiveTab,
            ]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab
                  ? styles.activeTabText
                  : styles.inactiveTabText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Championship Title and Year Selector */}
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.championshipTitle,
              { color: colors.primaryTextColor, width: "50%" },
            ]}
          >
            {activeTab} CHAMPIONSHIP
          </Text>
          <TouchableOpacity
            style={styles.yearSelector}
            onPress={showDatePickerModal}
          >
            <Text style={styles.yearText}>{selectedYear}</Text>
            <MaterialCommunityIcons
              name="chevron-down"
              size={20}
              color="#666"
            />
          </TouchableOpacity>
        </View>

        {/* Top 3 Cards */}
        {getCurrentData().slice(0, 3).map(renderTopCard)}

        {/* Detailed List Header */}
        <View style={styles.listHeader}>
          <Text style={styles.listHeaderText}>#</Text>
          <Text style={styles.listHeaderText}>{getHeaderText()}</Text>
        </View>

        {/* Detailed List */}
        {getCurrentData().slice(3).map(renderDetailedItem)}
      </ScrollView>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <Modal
          transparent={true}
          animationType="slide"
          visible={showDatePicker}
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Championship Year</Text>
                <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                  <MaterialCommunityIcons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>

              <DateTimePicker
                value={selectedDate}
                mode="date"
                display={Platform.OS === "ios" ? "spinner" : "default"}
                onChange={handleDateChange}
                maximumDate={new Date()}
                minimumDate={new Date(2020, 0, 1)}
              />

              {Platform.OS === "ios" && (
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.cancelButton]}
                    onPress={() => setShowDatePicker(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.confirmButton]}
                    onPress={() => setShowDatePicker(false)}
                  >
                    <Text style={styles.confirmButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
        </Modal>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "bold",
    textAlign: "center",
  },
  tabContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 12,
    backgroundColor: "#E5E5E5",
    // padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  activeTab: {
    backgroundColor: "#2E7D32",
  },
  inactiveTab: {
    backgroundColor: "transparent",
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
  },
  activeTabText: {
    color: "white",
  },
  inactiveTabText: {
    color: "#666",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  championshipTitle: {
    fontSize: 22,
    fontWeight: "bold",
    lineHeight: 30,
  },
  yearSelector: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "black",
  },
  yearText: {
    fontSize: 14,
    marginRight: 8,
    color: "black",
  },
  topCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 35,
    position: "relative",
  },
  rankBadge: {
    position: "absolute",
    top: -20,
    left: "50%",
    // marginLeft: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FFEBBC",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  rankText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  cardHeader: {
    // alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    flexDirection: "row",
  },
  ownerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  initialsCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(255,255,255,0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    borderWidth: 2,
    borderColor: "white",
  },
  initialsText: {
    fontSize: 20,
    fontWeight: "bold",
    color: "white",
  },
  ownerName: {
    fontSize: 26,
    fontWeight: "bold",
    color: "white",
    marginBottom: 8,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingTop: 20,
  },
  statItem: {
    flex: 1,
    alignItems: "flex-start",
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: "black",
    marginHorizontal: 20,
  },
  statLabel: {
    fontSize: 10,
    color: "black",
    fontWeight: "600",
    textAlign: "left",
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "800",
    color: "black",
    textAlign: "left",
  },
  bottomStats: {
    flexDirection: "row",
    alignItems: "flex-end",
    paddingHorizontal: 5,
    maxHeight: 140,
  },
  chartContainer: {
    marginRight: 5,
    alignItems: "center",
    justifyContent: "flex-end",
    minHeight: 120,
  },
  ridesBar: {
    backgroundColor: "rgba(255,255,255,0.8)",
    borderRadius: 4,
    width: 40,
    marginVertical: 4,
  },
  positionBar: {
    backgroundColor: "rgba(255,255,255,0.6)",
    borderRadius: 3,
    width: 25,
    marginVertical: 4,
  },
  ridesCount: {
    fontSize: 24,
    fontWeight: "bold",
    color: "white",
    lineHeight: 28,
    marginBottom: 4,
  },
  ridesLabel: {
    fontSize: 10,
    color: "rgba(255,255,255,0.9)",
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  positionStats: {
    flexDirection: "row",
    flex: 1,
    justifyContent: "space-around",
    alignItems: "flex-end",
  },
  positionItem: {
    backgroundColor: "rgba(255,255,255,0.25)",
    alignItems: "center",
    minWidth: 35,
    height: 80,
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  positionNumber: {
    fontSize: 20,
    fontWeight: "bold",
    color: "white",
    lineHeight: 24,
    marginBottom: 2,
  },
  positionLabel: {
    fontSize: 10,
    color: "rgba(255,255,255,0.9)",
    fontWeight: "600",
    letterSpacing: 0.3,
  },
  listHeader: {
    flexDirection: "row",
    backgroundColor: "#4A4A4A",
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  listHeaderText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
    // flex: 1,
    marginLeft: 16,
  },
  detailedItem: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  detailedHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  rankCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#2E7D32",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  rankCircleText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
  },
  initialsCircleSmall: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#E0E0E0",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  initialsTextSmall: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#666",
  },
  detailedName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
  },
  detailedStats: {
    marginLeft: 56,
  },
  detailedStatsText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  statBold: {
    fontWeight: "bold",
    color: "#333",
  },
  sectionHeader: {
    marginTop: 30,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  horseRaceCard: {
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  horseRaceHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  saddleNumberBadge: {
    backgroundColor: "#2E7D32",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  saddleNumberText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  horseInfo: {
    flex: 1,
    marginRight: 12,
  },
  horseName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 4,
  },
  horseDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  drawBadge: {
    backgroundColor: "#F0F0F0",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  drawText: {
    fontSize: 12,
    color: "#666",
    fontWeight: "600",
  },
  participantInfo: {
    borderTopWidth: 1,
    borderTopColor: "#E0E0E0",
    paddingTop: 12,
  },
  participantRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  participantLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    width: 80,
  },
  participantValue: {
    fontSize: 14,
    color: "#666",
    flex: 1,
    textAlign: "right",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    width: "90%",
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: "#f0f0f0",
  },
  confirmButton: {
    backgroundColor: "#2E7D32",
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "600",
  },
  confirmButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ChampionshipStandingsScreen;
