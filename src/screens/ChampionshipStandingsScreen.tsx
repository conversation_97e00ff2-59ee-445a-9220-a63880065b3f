import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useSelector } from 'react-redux';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const { width } = Dimensions.get('window');

interface StandingItem {
  id: number;
  name: string;
  initials: string;
  strikeRate: number;
  totalPrize: number;
  rides: number;
  first: number;
  second: number;
  third: number;
  fourth: number;
  rank: number;
}

const ChampionshipStandingsScreen = ({ navigation }: any) => {
  const colors = useSelector((state: any) => state.theme.colors);
  const [activeTab, setActiveTab] = useState('OWNERS');
  const [selectedYear, setSelectedYear] = useState('2024/2025');

  // Mock data - replace with actual data from your API
  const ownersData: StandingItem[] = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      initials: 'YR',
      strikeRate: 9.6,
      totalPrize: 4555570,
      rides: 481,
      first: 46,
      second: 64,
      third: 59,
      fourth: 47,
      rank: 1,
    },
    {
      id: 2,
      name: 'Al <PERSON>',
      initials: 'AR',
      strikeRate: 8.8,
      totalPrize: 3245890,
      rides: 398,
      first: 35,
      second: 52,
      third: 48,
      fourth: 41,
      rank: 2,
    },
    {
      id: 3,
      name: 'Khalid Khalifa Al Nabooda',
      initials: 'KN',
      strikeRate: 9.2,
      totalPrize: 2157890,
      rides: 336,
      first: 31,
      second: 35,
      third: 33,
      fourth: 28,
      rank: 3,
    },
  ];

  const detailedData = [
    {
      id: 4,
      name: 'Godolphin',
      initials: 'GD',
      rank: 4,
      first: 20,
      second: 15,
      third: 7,
      fourth: 9,
      totalRuns: 91,
      strikeRate: 22,
      totalPrize: 12645250,
    },
    {
      id: 8,
      name: 'Sh Hamdan bin Mohammed...',
      initials: 'SM',
      rank: 8,
      first: 8,
      second: 13,
      third: 9,
      fourth: 13,
      totalRuns: 154,
      strikeRate: 5.2,
      totalPrize: 1348440,
    },
    {
      id: 9,
      name: 'Mohammed Ahmad Ali Al...',
      initials: 'MS',
      rank: 9,
      first: 8,
      second: 8,
      third: 11,
      fourth: 9,
      totalRuns: 69,
      strikeRate: 11.6,
      totalPrize: 1912740,
    },
    {
      id: 10,
      name: 'Abubaker Kadoura',
      initials: 'AK',
      rank: 10,
      first: 8,
      second: 2,
      third: 3,
      fourth: 6,
      totalRuns: 32,
      strikeRate: 25,
      totalPrize: 1863710,
    },
  ];

  const renderTopCard = (item: StandingItem) => (
    <View key={item.id} style={[styles.topCard, { backgroundColor: getCardColor(item.rank) }]}>
      <View style={styles.rankBadge}>
        <Text style={styles.rankText}>{item.rank}</Text>
      </View>
      
      <View style={styles.cardHeader}>
        <View style={styles.initialsCircle}>
          <Text style={styles.initialsText}>{item.initials}</Text>
        </View>
        <Text style={styles.ownerName}>{item.name}</Text>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>STRIKE RATE</Text>
          <Text style={styles.statValue}>{item.strikeRate} %</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>TOTAL PRIZE (AED)</Text>
          <Text style={styles.statValue}>{item.totalPrize.toLocaleString()}</Text>
        </View>
      </View>

      <View style={styles.bottomStats}>
        <View style={styles.ridesBar}>
          <Text style={styles.ridesCount}>{item.rides}</Text>
          <Text style={styles.ridesLabel}>RIDES</Text>
        </View>
        <View style={styles.positionStats}>
          <View style={styles.positionItem}>
            <Text style={styles.positionNumber}>{item.first}</Text>
            <Text style={styles.positionLabel}>1ST</Text>
          </View>
          <View style={styles.positionItem}>
            <Text style={styles.positionNumber}>{item.second}</Text>
            <Text style={styles.positionLabel}>2ND</Text>
          </View>
          <View style={styles.positionItem}>
            <Text style={styles.positionNumber}>{item.third}</Text>
            <Text style={styles.positionLabel}>3RD</Text>
          </View>
          <View style={styles.positionItem}>
            <Text style={styles.positionNumber}>{item.fourth}</Text>
            <Text style={styles.positionLabel}>4TH</Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderDetailedItem = (item: any) => (
    <View key={item.id} style={styles.detailedItem}>
      <View style={styles.detailedHeader}>
        <View style={styles.rankCircle}>
          <Text style={styles.rankCircleText}>{item.rank}</Text>
        </View>
        <View style={styles.initialsCircleSmall}>
          <Text style={styles.initialsTextSmall}>{item.initials}</Text>
        </View>
        <Text style={styles.detailedName}>{item.name}</Text>
      </View>
      
      <View style={styles.detailedStats}>
        <Text style={styles.detailedStatsText}>
          <Text style={styles.statBold}>1ST</Text> {item.first} <Text style={styles.statBold}>2ND</Text> {item.second} <Text style={styles.statBold}>3RD</Text> {item.third} <Text style={styles.statBold}>4TH</Text> {item.fourth} <Text style={styles.statBold}>R</Text> {item.totalRuns}
        </Text>
        <Text style={styles.detailedStatsText}>
          <Text style={styles.statBold}>SR (%)</Text> {item.strikeRate} <Text style={styles.statBold}>TP AED</Text> {item.totalPrize.toLocaleString()}
        </Text>
      </View>
    </View>
  );

  const getCardColor = (rank: number) => {
    switch (rank) {
      case 1: return '#F4B942'; // Gold
      case 2: return '#C0C0C0'; // Silver
      case 3: return '#CD7F32'; // Bronze
      default: return '#E0E0E0'; // Gray
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.backgroundColor }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <MaterialCommunityIcons name="chevron-left" size={24} color={colors.textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.textColor }]}>CHAMPIONSHIP STANDINGS</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {['OWNERS', 'TRAINERS', 'JOCKEYS'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              activeTab === tab ? styles.activeTab : styles.inactiveTab,
            ]}
            onPress={() => setActiveTab(tab)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab ? styles.activeTabText : styles.inactiveTabText,
              ]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Championship Title and Year Selector */}
        <View style={styles.titleContainer}>
          <Text style={[styles.championshipTitle, { color: colors.textColor }]}>
            {activeTab} CHAMPIONSHIP
          </Text>
          <TouchableOpacity style={styles.yearSelector}>
            <Text style={styles.yearText}>{selectedYear}</Text>
            <MaterialCommunityIcons name="chevron-down" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Top 3 Cards */}
        {ownersData.slice(0, 3).map(renderTopCard)}

        {/* Detailed List Header */}
        <View style={styles.listHeader}>
          <Text style={styles.listHeaderText}>#</Text>
          <Text style={styles.listHeaderText}>OWNER'S NAME</Text>
        </View>

        {/* Detailed List */}
        {detailedData.map(renderDetailedItem)}
      </ScrollView>
    </SafeAreaView>
  );
};

const getCardColor = (rank: number) => {
  switch (rank) {
    case 1: return '#F4B942'; // Gold
    case 2: return '#C0C0C0'; // Silver  
    case 3: return '#CD7F32'; // Bronze
    default: return '#E0E0E0'; // Gray
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 20,
    borderRadius: 25,
    backgroundColor: '#E5E5E5',
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 20,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#2E7D32',
  },
  inactiveTab: {
    backgroundColor: 'transparent',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  activeTabText: {
    color: 'white',
  },
  inactiveTabText: {
    color: '#666',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  championshipTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  yearSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  yearText: {
    fontSize: 14,
    marginRight: 8,
    color: '#333',
  },
  topCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    position: 'relative',
  },
  rankBadge: {
    position: 'absolute',
    top: -10,
    left: '50%',
    marginLeft: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  rankText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  cardHeader: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  initialsCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'white',
  },
  initialsText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  ownerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: 20,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 4,
    textAlign: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  bottomStats: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  ridesBar: {
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 8,
    padding: 12,
    marginRight: 20,
    alignItems: 'center',
    minWidth: 60,
  },
  ridesCount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  ridesLabel: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 4,
  },
  positionStats: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-around',
  },
  positionItem: {
    alignItems: 'center',
  },
  positionNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  positionLabel: {
    fontSize: 10,
    color: 'rgba(255,255,255,0.8)',
    marginTop: 4,
  },
  listHeader: {
    flexDirection: 'row',
    backgroundColor: '#4A4A4A',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  listHeaderText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
  },
  detailedItem: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  detailedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rankCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  rankCircleText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  initialsCircleSmall: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  initialsTextSmall: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
  },
  detailedName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  detailedStats: {
    marginLeft: 56,
  },
  detailedStatsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  statBold: {
    fontWeight: 'bold',
    color: '#333',
  },
});

export default ChampionshipStandingsScreen;
