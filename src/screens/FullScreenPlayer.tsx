import React, { useRef, useEffect } from "react";
import { View, Dimensions, TouchableHighlight } from "react-native";
import { StatusBar } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/Ionicons";
import Orientation from "react-native-orientation";
import Player from "../components/Player";

const { width, height } = Dimensions.get("window");

const FullscreenVideoPlayer = ({ route, navigation }) => {
  const playerRef = useRef([]);
  const { itemurl, image } = route.params;
  const [show, setShow] = React.useState(false);
  const timer = setTimeout(() => {
    setShow(false);
  }, 2600);
  const onTime = (e) => {
    // var {position, duration} = e.nativeEvent;
    // eslint-disable-line
    // console.log('onTime was called with: ', position, duration);
  };

  const onFullScreen = () => {
    StatusBar.setHidden(true);
  };

  const onFullScreenExit = () => {
    StatusBar.setHidden(false);
  };

  const setclose = () => {
    clearTimeout(timer);
    setShow((s) => !s);
    // timer reference handled above
  };

  useEffect(() => {
    console.log("dxsfgsxdghdxzdgxhgc", itemurl);
    StatusBar.setHidden(true);
    setclose();
    // Orientation.lockToLandscape();
    // navigation.goBack(null);
  }, []);

  useEffect(() => {
    return () => {
      StatusBar.setHidden(false);
      Orientation.lockToPortrait();
    };
  }, []);

  return (
    StatusBar.setHidden(true),
    (
      <TouchableHighlight
        onPress={() => setclose()}
        //
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          height: height,
          width: width,
          backgroundColor: "black",
        }}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            height: height,
            width: width,
            backgroundColor: "black",
            marginBottom: width / 30,
          }}
        >
          <View
            style={{
              height: width,
              width: height / 1.1,
              transform: [{ rotate: "270deg" }],
            }}
            onPress={() => setShow(true)}
          >
            <Player
              ref={playerRef}
              style={{ flex: 1 }}
              config={{
                autostart: true,
                playlist: [
                  {
                    file:
                      "https://cdn.jwplayer.com/manifests/" + itemurl + ".m3u8",
                    image: image,
                  },
                ],
                styling: {
                  colors: {},
                },
              }}
              onTime={onTime}
              onFullScreen={onFullScreen}
              onFullScreenExit={onFullScreenExit}
            />
            {show ? (
              <TouchableHighlight
                onPress={() => navigation.goBack(null)}
                style={{
                  width: Dimensions.get("window").width / 20,
                  marginTop: -Dimensions.get("window").width / 1.05,
                  marginBottom: Dimensions.get("window").width / 1.113,
                }}
              >
                <View>
                  <MaterialCommunityIcons
                    name="close"
                    color={"white"}
                    size={Dimensions.get("window").width / 20}
                  />
                </View>
              </TouchableHighlight>
            ) : null}
          </View>
        </View>
      </TouchableHighlight>
    )
  );
};
export default FullscreenVideoPlayer;
