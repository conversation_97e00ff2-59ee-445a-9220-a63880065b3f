import {
  ActivityIndicator,
  FlatList,
  SafeAreaView,
  StyleSheet,
  Text,
  ScrollView,
  TouchableOpacity,
  View,
  Dimensions,
  ImageBackground,
} from "react-native";
import { BlurView } from "@react-native-community/blur";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../redux/store";
import {
  fetchData,
  incrementListCount,
  setActiveTabState,
} from "../redux/MainScreenSlice";
import Banner from "../components/Banner";
import NewArticleFullImage from "../components/NewArticleFullImage";
import LandscapeList from "../components/LandscapeList";
import NewsLandscape from "../components/NewsLandScape";
import TextGrid from "../components/TextGrid";
import FullLandscapeListImageView from "../components/FullLandscapeListImageView";
import FullLandscapeList from "../components/FullLandscapeList";
import AdBanner from "../components/AdBanner";
import PortraitList from "../components/PortraitList";
import BigPortraitList from "../components/BigPortraitList";
import HightLightTextGrid from "../components/HighLightTextGrid";
import {
  useFocusEffect,
  useNavigation,
  CommonActions,
  useRoute,
} from "@react-navigation/native";
import ThreeDLandscapeList from "../components/ThreeDLandscapeList";
import LandscapeListDes from "../components/LandscapeListDesc";
import MovieCarouselList from "../components/MovieCarousalList";
import SquareList from "../components/SquareList";
import FullLandscapeListImageViewDouble from "../components/FullLandscapeListImageViewDouble";
import TabListLandscape from "../components/TabListLandscape";
import ThreeDCarousal from "../components/ThreeDCarousel";
import VideoPlayer from "../components/VideoPlayer";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { iconMap } from "../utils/constants";

const HomeScreen: React.FC = (props) => {
  console.log("HomeScreen Props =>", props);
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();
  const { car, isLoading, listCount, totalListCount } = useSelector(
    (state: RootState) => state.mainScreen
  );
  const { width } = Dimensions.get("window");
  const { colors, topMenu, appSettings } = useSelector(
    (state: RootState) => state.homeScreen
  );
  const [isDataLoading, setIsDataLoading] = useState<boolean>(true);
  const [components, setComponents] = useState<{ compName: JSX.Element }[]>([]);
  const [activeTabIndex, setActiveTabIndex] = useState(-1);
  const [topTabIconVisibility, setTopTabIconVisibility] = useState(false); // for future use

  // console.log("HomeScreen State =>", car, isLoading, listCount, totalListCount);

  // console.log("HomeScreen Route Props =>", route?.params);

  let mergedProps = {};
  const mergedPropsRef = useRef(mergedProps);

  const fetchDataAsync = async () => {
    if (mergedProps.urlname) {
      setIsDataLoading(true);
      await dispatch(fetchData({ urlname: mergedProps.urlname, listCount }));
      setTimeout(() => {
        setIsDataLoading(false);
      }, 3000);
    }
  };

  const initProps = () => {
    try {
      if (route.params) {
        mergedProps = { ...props, ...(route.params || {}) };
      } else {
        mergedProps = props;
      }

      mergedPropsRef.current = mergedProps;
    } catch (error) {
      console.log("error while trying to merge props [home]: ", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      initProps();
      setActiveTabIndex(mergedPropsRef.current.activeTopTabIndex ?? -1);
      fetchDataAsync();
      return () => {};
    }, [route?.params, listCount])
  );

  useEffect(() => {
    const newComponents = car
      .filter(
        (item) =>
          item.itemid.devices?.[2]?.itemName === "iOS" &&
          item.itemid.devices?.[2]?.itemVisibility === true
      )
      .map((item) => {
        console.log(
          "item.itemid.elementType ------------------",
          item.itemid.elementType
        );

        switch (item.itemid.elementType) {
          case "adBanner":
            return {
              compName: (
                <AdBanner
                  listtitle={item.itemid.displayName}
                  seemoretext={""}
                  url={"https://www.google.com"}
                  urltype={"IN"}
                  datas={item.itemid}
                ></AdBanner>
              ),
            };
          case "portraitSlider":
            return {
              compName: (
                <PortraitList
                  seemorecomp={"FullPortraitList"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></PortraitList>
              ),
            };
          case "landscapeSlider":
            return {
              compName: (
                <FullLandscapeList
                  seemorecomp={"FullLandscapeList"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></FullLandscapeList>
              ),
            };
          case "newsCarousel":
            return {
              compName: (
                <Banner
                  listtitle={item.itemid.displayName}
                  seemoretext={""}
                  datas={item.itemid}
                ></Banner>
              ),
            };
          case "movieCarousel":
            return {
              compName: (
                <MovieCarouselList
                  seemorecomp={"movieCarousel"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                  height={3 / 4}
                ></MovieCarouselList>
              ),
            };
          case "squareSlider":
            return {
              compName: (
                <SquareList
                  seemorecomp={"SquareList"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></SquareList>
              ),
            };
          // return { compName: <Text>SquareList</Text> };
          case "articleSlider":
            return {
              compName: (
                <NewsLandscape
                  seemorecomp={"FullListPortrait"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                  color={""}
                ></NewsLandscape>
              ),
            };
          case "newsList":
            return {
              compName: (
                <NewArticleFullImage
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></NewArticleFullImage>
              ),
            };
          case "bigportraitSlider":
            return {
              compName: (
                <BigPortraitList
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></BigPortraitList>
              ),
            };
          case "newsSlider":
            return {
              compName: (
                <NewsLandscape
                  seemorecomp={"FullListPortrait"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                  color={"red"}
                ></NewsLandscape>
              ),
            };
          case "3dCarousel":
            // return { compName: <Text>ThreedCarousal</Text> };
            return {
              compName: (
                <ThreeDCarousal
                  seemorecomp={"ThreedCarousal"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></ThreeDCarousal>
              ),
            };
          case "multiCarousel":
            return {
              compName: (
                <ThreeDLandscapeList
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></ThreeDLandscapeList>
              ),
            };
          case "tabItem":
            return {
              compName: (
                <TabListLandscape
                  seemorecomp={"TabListLandscape"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></TabListLandscape>
              ),
            };
          case "grid":
            return {
              compName: (
                <FullLandscapeListImageView
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></FullLandscapeListImageView>
              ),
            };
          case "doubleGrid":
            console.log("FullLandscapeListImageViewdouble--->", item.itemid);
            return {
              compName: (
                <FullLandscapeListImageViewDouble
                  seemorecomp={"FullLandscapeListImageViewdouble"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></FullLandscapeListImageViewDouble>
              ),
            };
          case "textGrid":
            return {
              compName: (
                <TextGrid
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></TextGrid>
              ),
            };
          case "articleList":
            return {
              compName: (
                <LandscapeListDes
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></LandscapeListDes>
              ),
            };
          case "highlightTextGrid":
            return {
              compName: (
                <HightLightTextGrid
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></HightLightTextGrid>
              ),
            };
          case "landscapeList":
            return {
              compName: (
                <FullLandscapeList
                  seemorecomp={"LandscapeListDec"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></FullLandscapeList>
              ),
            };
          case "portraitList":
            return {
              compName: (
                <PortraitList
                  seemorecomp={"FullPortraitList"}
                  listtitle={item.itemid.displayName}
                  seemoretext={item.itemid.viewAllText}
                  seemoretextvis={item.itemid.viewAllVisibility}
                  datas={item.itemid}
                ></PortraitList>
              ),
            };
          default:
            return null;
        }
      })
      .filter(Boolean) as { compName: JSX.Element }[];

    setComponents(newComponents);
  }, [car]); // Updates when `car` changes

  const renderLoader = () => {
    return isLoading ? (
      <View style={styles(colors).loaderStyle}>
        <ActivityIndicator size="small" color="#aaa" />
      </View>
    ) : null;
  };

  const LoadMoreItem = () => {
    dispatch(incrementListCount());
    dispatch(
      fetchData({
        urlname: mergedPropsRef.current.urlname ?? "home",
        listCount,
      })
    );
  };

  const handleTopMenuNav = (index: number, item) => {
    if (index != -1) {
      dispatch(setActiveTabState(false));
    }
    setActiveTabIndex(index);
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          {
            name: "Home",
            params: {
              activeTopTabIndex: index,
              color: colors,
              appsettinglist: appSettings,
              title: item.menuTitle,
              urlname: item.urlName,
            },
          },
        ],
      })
    );
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor:
          mergedPropsRef.current.color &&
          mergedPropsRef.current.color.primaryBackgroundColor,
      }}
    >
      <ImageBackground
        source={require("../../assets/bg.jpg")}
        style={{ flex: 1, width: "100%", height: "100%" }}
        resizeMode="cover"
      >
        {/* <BlurView
          style={StyleSheet.absoluteFill}
          blurType="light"
          blurAmount={10}
          reducedTransparencyFallbackColor="white"
        /> */}
        <BlurView
          style={StyleSheet.absoluteFillObject}
          blurType="light" // options: light, dark, extraLight, etc.
          blurAmount={8} // Adjust for more/less blur
          reducedTransparencyFallbackColor="white"
        />
        {isDataLoading ? (
          <View
            style={[
              styles(colors).loaderStyle,
              { justifyContent: "center", flex: 1 },
            ]}
          >
            <ActivityIndicator size="large" color="#aaa" />
          </View>
        ) : (
          <View style={styles(colors).container}>
            {topMenu && (
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={{ height: 50, backgroundColor: "red" }}
              >
                <View
                  style={[
                    styles(colors).tabRow,
                    { backgroundColor: colors.menuBackgroundColor, height: 50 },
                  ]}
                >
                  {topMenu.map((tab, idx) => {
                    const iconName = iconMap[tab.menuIcon] || "home";

                    return (
                      <TouchableOpacity
                        key={idx}
                        onPress={() => handleTopMenuNav(idx, tab)}
                        style={[
                          styles(colors).tabButton,
                          activeTabIndex === idx &&
                            styles(colors).activeTabText,
                        ]}
                      >
                        {topTabIconVisibility && (
                          <MaterialCommunityIcons
                            name={iconName || "home"}
                            color={
                              activeTabIndex === idx
                                ? colors.primaryHighlightColor
                                : colors.primaryTextColor
                            }
                            size={width / 20}
                          />
                        )}
                        <Text
                          style={[
                            styles(colors).tabText,
                            activeTabIndex === idx &&
                              styles(colors).activeTabText,
                          ]}
                        >
                          {tab.menuTitle}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </ScrollView>
            )}
            <FlatList
              ListHeaderComponent={<VideoPlayer />} // Moves VideoPlayer outside ScrollView
              showsHorizontalScrollIndicator={false}
              data={components}
              renderItem={({ item }) => item.compName} //
              ListFooterComponent={renderLoader}
              onEndReached={car.length !== totalListCount ? LoadMoreItem : null}
              onEndReachedThreshold={0}
              contentContainerStyle={{}}
              style={{ marginHorizontal: 5 }}
            />
          </View>
        )}
      </ImageBackground>
    </SafeAreaView>
  );
};

export default HomeScreen;

export const styles = (colors: Color) =>
  StyleSheet.create({
    loaderStyle: {
      marginVertical: 16,
      alignItems: "center",
    },
    container: {
      flex: 1,
    },
    tabRow: {
      flexDirection: "row",
      justifyContent: "space-around",
      paddingVertical: 10,
      elevation: 8, // Adds shadow for Android
      borderBottomWidth: 1,
      borderColor: "#ccc", // shadowColor: '#000', // Shadow color for iOS
      shadowOffset: { width: 0, height: 2 }, // Shadow offset for iOS
      shadowOpacity: 0.25, // Shadow opacity for iOS
      shadowRadius: 3.84, // Shadow radius for iOS
    },
    tabButton: {
      paddingVertical: 8, // Ensures vertical padding
      paddingHorizontal: 16,
      justifyContent: "center",
      alignItems: "center",
      height: 40,
      marginHorizontal: 5,
      flexDirection: "column",
      marginBottom: 5,
    },
    activeTabText: {
      color: colors.primaryHighlightColor,
    },
    tabText: {
      color: colors.primaryTextColor,
      fontWeight: "600",
      fontSize: 12,
      lineHeight: 18, // Helps prevent vertical clipping
      marginTop: 4,
    },

    content: {
      flex: 1,
      margin: 16,
      borderWidth: 1,
      borderColor: "#ccc",
      borderRadius: 8,
      justifyContent: "center",
      alignItems: "center",
    },
  });
