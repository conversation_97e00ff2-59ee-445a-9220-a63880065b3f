import axios from "axios";
import {
  updateAppLangDirection,
  updateAppLanguage,
  updateAppSettings,
  updateBrand,
  updateColors,
  updateImageKey,
  updateMenu,
  updateTopMenu,
  updateVideoInfo,
} from "../redux/HomeScreenSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { store } from "../redux/store";
import { updateLivePageUrl } from "../redux/LiveStreamSlice";

export class AppService {
  static baseUrl = "https://smott-api-demo.technitysystems.com";
  static appId = store.getState().authScreen.appId; // for dev

  static async initApp(dispatch: Dispatch): Promise<void> {
    await AppService.getFullAppSetup(dispatch);
    await AppService.imageKeyId(dispatch);
    const darkMode = await AppService.getItemValue();
    await AppService.color(dispatch, darkMode);
  }

  static async color(dispatch: Dispatch, Dark: boolean): Promise<void> {
    const color = await AppService.getColor(Dark);
    dispatch(updateColors(color));
  }

  static async getColor(Dark: boolean): Promise<void> {
    await AppService.getItemValue();
    const token = await AppService.getToken();
    try {
      const result = await axios.get(
        AppService.baseUrl + `/api/view/${AppService.appId}/getAppSetup`,
        { headers: { Authorization: token } }
      );
      return Dark
        ? {
            application: AppService.appId,
            dark: result.data[0].color[0].dark,
            date: "2021-08-16T16:31:31.548Z",
            elementBackgroundColor: "#0d051499",
            elementForegroundColor:
              result.data[0].color[0].elementForegroundColor,
            menuBackgroundColor: "#000000",
            primaryBackgroundColor: "#000000",
            primaryHighlightColor: "#005aff",
            primaryTextColor: "#ffffff",
            secondaryBackgroundColor: "#000000",
            secondaryHighlightColor: "#195fdf",
            secondaryTextColor: "#333333",
            textShadow: result.data[0].color[0].textShadow,
            __v: 0,
            _id: "611a92e3e288ff29dc262e31",
            // Add missing properties for dark mode
            buttonBackgroundColor: "#005aff",
            buttonForegroundColor: "#ffffff",
            menuForegroundColor: "#ffffff",
          }
        : result.data[0].color[0];
    } catch (e) {
      console.log(e);
      throw e;
    }
  }

  static async getToken(): Promise<string> {
    const url =
      AppService.baseUrl + "/api/view/" + AppService.appId + "/accessToken";
    try {
      const result = await axios.post(url);
      // console.log(result.data);
      return result.data;
    } catch (e) {
      console.log("getToken =>", e);
      return e.toString();
    }
  }

  static async getItemValue(): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem("darkmode");
      //   const darkModeValue = value === 'true' ? true : false;
      return value === "true" ? true : false;
    } catch (exception) {
      return false;
      console.log("exception getItemValue", exception);
    }
  }

  static async getFullAppSetup(dispatch: Dispatch): Promise<void> {
    const token = await AppService.getToken();
    try {
      const result = await axios.get(
        AppService.baseUrl +
          "/api/view/" +
          AppService.appId +
          "/getAppSetup?device=app",
        { headers: { Authorization: token } }
      );
      console.log("result--------", result);
      const languageData = result.data[0].languages ?? [];
      const videoInfo = result.data[0].videocloud ?? []; // specifies if the video is youtube/ jwplayer/ inplayer
      const menuList: [] = result.data[0].menu;
      let updatedMenuList = menuList.filter(
        (item) => item.menuTitle !== "Home"
      );

      const topMenuList = updatedMenuList.filter(
        (item) => !item.position || item.position === "top"
      );
      updatedMenuList = updatedMenuList.filter(
        (item) => item.position && item.position == "bottom"
      );

      console.log("topMenuList: ", topMenuList);
      console.log("updatedMenuList: ", updatedMenuList);

      if (languageData && languageData.length > 0) {
        // sets app language
        // RTL if arabic
        // LTR for the rest- english, malayalam, ...
        dispatch(updateAppLanguage(languageData[0].languageRef));
        if (languageData[0].languageid === "ar") {
          dispatch(updateAppLangDirection(true));
        } else {
          dispatch(updateAppLangDirection(false));
        }
      }
      dispatch(updateMenu(updatedMenuList));
      dispatch(updateTopMenu(topMenuList));
      dispatch(updateBrand(result.data[0].branding[0]));
      dispatch(updateAppSettings(result.data[0].appsetting[0]));
      dispatch(updateVideoInfo(videoInfo));
    } catch (e) {
      console.log("error in videoDetail", e);
      throw e;
    }
  }

  static async imageKeyId(dispatch: Dispatch): Promise<void> {
    const token = await AppService.getToken();
    const branded = store.getState().homeScreen.brand;
    const Dark = false;
    try {
      const result = await axios.get(
        Dark === true
          ? AppService.baseUrl +
              "/api/aws/" +
              branded.mobileDarkLogo +
              "/getImage"
          : AppService.baseUrl + "/api/aws/" + branded.mobileLogo + "/getImage",
        { headers: { Authorization: token } }
      );
      dispatch(updateImageKey(result.data.images.imagekey));
    } catch (e) {
      console.log("error in imagekeyid", e);
      throw e;
    }
  }

  static async fetchLiveStreamData(dispatch: Dispatch): Promise<void> {
    const token = await AppService.getToken();

    const url =
      AppService.baseUrl +
      "/api/view/livePage/" +
      AppService.appId +
      "/getUrlFullItem";
    console.log("live-->", url);

    try {
      const result = await axios.get(url, {
        params: { limit: 1, skip: 0 },
        headers: { Authorization: token },
      });
      console.log("result.data.livePage------->", result.data.livePage);
      dispatch(updateLivePageUrl(result.data.livePage));
      return result.data;
    } catch (e) {
      console.log("fetchLiveStreamData =>", e);
      return e.toString();
    }
  }
}
