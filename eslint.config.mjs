import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import pluginReactNative from "eslint-plugin-react-native";

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    ignores: [
      "node_modules/**",
      "ios/build/**",
      "ios/Pods/**",
      "ios/**/*.xcframework/**",
      "android/build/**",
      "android/app/build/**",
      "android/**/intermediates/**",
      "**/*.generated.js",
      "**/build/**",
      "**/dist/**",
      "coverage/**",
      ".expo/**",
      "web-build/**",
      "**/jwplayer/**",
      "**/*-plugin-sdk.js",
      "**/*provider*.js",
      "**/omsdk-*.js",
      "**/jwplayer.*.js"
    ]
  },
  {
    files: ["**/*.{js,mjs,cjs,ts,jsx,tsx}"]
  },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2021,
        __DEV__: "readonly",
        fetch: "readonly",
        FormData: "readonly",
        navigator: "readonly",
        XMLHttpRequest: "readonly"
      },
      ecmaVersion: 2021,
      sourceType: "module"
    }
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    plugins: {
      "react-native": pluginReactNative
    },
    settings: {
      react: {
        version: "18.3.1"
      }
    },
    rules: {
      // React Native specific rules
      "react-native/no-unused-styles": "warn",
      "react-native/split-platform-components": "warn",
      "react-native/no-inline-styles": "off",
      "react-native/no-color-literals": "off",
      "react-native/no-raw-text": "off",

      // React rules adjustments
      "react/react-in-jsx-scope": "off", // Not needed in React 17+
      "react/prop-types": "off", // Using TypeScript for prop validation
      "react/display-name": "off",

      // TypeScript rules adjustments
      "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-empty-function": "off",
      "@typescript-eslint/ban-ts-comment": "warn",

      // General rules
      "no-console": "warn",
      "no-debugger": "warn",
      "no-unused-vars": "off", // Using TypeScript version instead
      "prefer-const": "warn"
    }
  }
];